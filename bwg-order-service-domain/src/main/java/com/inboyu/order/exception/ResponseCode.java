package com.inboyu.order.exception;

import com.inboyu.spring.cloud.starter.common.constant.ResponseCodeInterface;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025年07月29日 20:04
 */
public enum ResponseCode implements ResponseCodeInterface {

    /** 通用成功 */
    SUCCESS(0, "成功"),

    /**
     * 报修配置相关 (15100-15199)
     */
    REPAIR_CONFIG_NOT_EXIST(15100, "报修配置不存在"),
    REPAIR_CONFIG_ALREADY_EXIST(15101, "该门店已存在报修配置，请勿重复创建"),
    REPAIR_CONFIG_TIME_INVALID(15102, "报修时间配置不合理，开始时间必须早于结束时间"),
    REPAIR_CONFIG_STORE_NOT_EXIST(15103, "门店不存在"),

    /**
     * 报修物品相关 (15200-15299)
     */
    REPAIR_ITEM_NOT_EXIST(15200, "报修物品不存在"),
    REPAIR_ITEM_NOT_ACTIVE(15201, "报修物品未启用"),
    ;

    /** 订单服务错误码范围 15000 ～ 15999*/
    private final int code;
    private final String msg;

    ResponseCode(int code, String des) {
        this.code = code;
        this.msg = des;
    }

    @Override
    public String getMsg() {
        return this.msg;
    }

    @Override
    public Integer getCode() {
        return this.code;
    }
}
