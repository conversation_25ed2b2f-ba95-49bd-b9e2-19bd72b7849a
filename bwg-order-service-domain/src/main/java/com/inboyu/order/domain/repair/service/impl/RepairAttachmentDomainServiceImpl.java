package com.inboyu.order.domain.repair.service.impl;

import com.inboyu.order.domain.repair.model.AttachmentId;
import com.inboyu.order.domain.repair.model.RepairAttachment;
import com.inboyu.order.domain.repair.model.RepairOrderId;
import com.inboyu.order.domain.repair.repository.RepairOrderRepository;
import com.inboyu.order.domain.repair.service.RepairAttachmentDomainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 报修附件领域服务实现
 * 专门处理报修附件相关的业务逻辑
 */
@Service
public class RepairAttachmentDomainServiceImpl implements RepairAttachmentDomainService {

    @Autowired
    private RepairOrderRepository repairOrderRepository;

    // 文件大小限制（10MB）
    private static final Long MAX_FILE_SIZE = 10 * 1024 * 1024L;

    @Override
    public AttachmentId generateAttachmentId() {
        return repairOrderRepository.generateAttachmentId();
    }

    @Override
    public List<RepairAttachment> batchCreateAttachments(List<RepairAttachment> attachments, RepairOrderId repairOrderId) {
        if (attachments == null || attachments.isEmpty()) {
            return List.of();
        }

        // 批量创建附件（为每个附件生成ID）
        List<RepairAttachment> attachmentsWithId = attachments.stream()
                .map(attachment -> RepairAttachment.builder()
                        .attachmentId(generateAttachmentId())
                        .fileName(attachment.getFileName())
                        .fileUrl(attachment.getFileUrl())
                        .fileType(attachment.getFileType())
                        .attachmentType(attachment.getAttachmentType())
                        .fileSize(attachment.getFileSize())
                        .uploadTime(attachment.getUploadTime())
                        .build())
                .collect(Collectors.toList());

        return attachmentsWithId;
    }

    @Override
    public RepairAttachment createAttachment(RepairAttachment attachment, RepairOrderId repairOrderId) {

        // 生成附件ID
        AttachmentId attachmentId = generateAttachmentId();

        // 创建包含ID的附件
        return RepairAttachment.builder()
                .attachmentId(attachmentId)
                .fileName(attachment.getFileName())
                .fileUrl(attachment.getFileUrl())
                .fileType(attachment.getFileType())
                .attachmentType(attachment.getAttachmentType())
                .fileSize(attachment.getFileSize())
                .uploadTime(attachment.getUploadTime())
                .build();
    }

    @Override
    public List<RepairAttachment> findAttachmentsByOrderId(RepairOrderId repairOrderId) {
        // 这里可以通过Repository查询附件
        // 暂时返回空列表，具体实现需要根据实际需求
        return List.of();
    }

    @Override
    public boolean deleteAttachment(AttachmentId attachmentId) {
        // 删除附件的业务逻辑
        // 1. 验证是否可以删除
        // 2. 执行删除操作
        // 3. 可能需要删除实际文件
        return true;
    }
}
