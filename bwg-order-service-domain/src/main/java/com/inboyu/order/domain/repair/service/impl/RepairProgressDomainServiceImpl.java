package com.inboyu.order.domain.repair.service.impl;

import com.inboyu.order.domain.repair.model.*;
import com.inboyu.order.domain.repair.repository.RepairOrderRepository;
import com.inboyu.order.domain.repair.service.RepairProgressDomainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 报修进度领域服务实现
 * 专门处理报修进度相关的业务逻辑
 */
@Service
public class RepairProgressDomainServiceImpl implements RepairProgressDomainService {

    @Autowired
    private RepairOrderRepository repairOrderRepository;

    @Override
    public RepairProgressId generateProgressId() {
        return repairOrderRepository.generateProgressId();
    }

    @Override
    public void createSubmitProgress(RepairOrder repairOrder) {

        // 生成进度ID
        RepairProgressId progressId = generateProgressId();

        // 创建提交进度
        RepairProgress submitProgress = RepairProgress.createSubmitProgress(progressId, repairOrder);

        // 保存进度
        repairOrderRepository.saveProgress(submitProgress);
    }


    /**
     * 获取报修订单进度列表
     *
     * @param orderId 订单ID
     * @return 进度列表
     */
    public List<RepairProgress> getRepairOrderProgressList(RepairOrderId orderId) {
        // 验证订单是否存在
        RepairOrder repairOrder = repairOrderRepository.findByRepairOrderId(orderId);
        if (repairOrder == null) {
            throw new IllegalArgumentException("报修订单不存在");
        }

        return repairOrderRepository.findProgressListByRepairOrderId(orderId);
    }


    /**
     * 创建取消进度记录
     *
     * @param repairOrder 报修订单
     * @param cancelReason 取消原因
     */
    public void createCancelProgress(RepairOrder repairOrder, String cancelReason) {
        // 生成进度ID
        RepairProgressId progressId = repairOrderRepository.generateProgressId();

        // 创建取消进度记录
        RepairProgress cancelProgress = RepairProgress.createCancelProgress(progressId, repairOrder, cancelReason);
        repairOrderRepository.saveProgress(cancelProgress);
    }

    /**
     * 创建确认进度记录
     *
     * @param repairOrder 报修订单
     * @param confirmType 确认类型
     * @param confirmComment 确认备注
     * @param reworkInstructions 返工说明
     */
    public void createConfirmProgress(RepairOrder repairOrder, String confirmType, String confirmComment, String reworkInstructions) {
        // 生成进度ID
        RepairProgressId progressId = repairOrderRepository.generateProgressId();

        // 创建确认进度记录
        RepairProgress confirmProgress = RepairProgress.createConfirmProgress(progressId, repairOrder, confirmType, confirmComment, reworkInstructions);
        repairOrderRepository.saveProgress(confirmProgress);
    }

    /**
     * 创建受理/不受理进度记录
     *
     * @param repairOrder 报修订单
     * @param operationType 操作类型 (accept/reject)
     * @param operationComment 操作说明
     */
    public void createAcceptRejectProgress(RepairOrder repairOrder, String operationType, String operationComment) {
        // 生成进度ID
        RepairProgressId progressId = repairOrderRepository.generateProgressId();

        // 创建受理/不受理进度记录
        RepairProgress acceptRejectProgress = RepairProgress.createAcceptRejectProgress(progressId, repairOrder, operationType, operationComment);
        repairOrderRepository.saveProgress(acceptRejectProgress);
    }

    /**
     * 创建完成维修进度记录
     *
     * @param repairOrder 报修订单
     * @param maintainerName 维修人员姓名
     * @param completeComment 完成说明
     */
    public void createCompleteProgress(RepairOrder repairOrder, String maintainerName, String completeComment) {
        // 生成进度ID
        RepairProgressId progressId = repairOrderRepository.generateProgressId();

        // 创建完成维修进度记录
        RepairProgress completeProgress = RepairProgress.createCompleteProgress(progressId, repairOrder, maintainerName, completeComment);
        repairOrderRepository.saveProgress(completeProgress);
    }
}
