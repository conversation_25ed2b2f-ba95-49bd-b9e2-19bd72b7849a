package com.inboyu.order.domain.repair.model;

import lombok.Builder;
import lombok.Getter;

/**
 * 报修物品领域对象
 * 包含报修物品的基本信息和业务行为
 */
@Getter
@Builder
public class RepairItem {

    private final RepairItemId id;
    private final String kind;
    private final String name;
    private final String icon;
    private final String url;
    private final String remark;
    private final Integer sortOrder;
    private final Boolean isActive;

    /**
     * 创建报修物品
     *
     * @param id        物品ID
     * @param kind      物品分类
     * @param name      物品名称
     * @param icon      物品图标
     * @param url       Logo地址
     * @param remark    故障描述标签
     * @param sortOrder 排序
     * @param isActive  是否启用
     * @return 报修物品对象
     */
    public static RepairItem create(RepairItemId id, String kind, String name, String icon, 
                                   String url, String remark, Integer sortOrder, Boolean isActive) {
        return RepairItem.builder()
                .id(id)
                .kind(kind)
                .name(name)
                .icon(icon)
                .url(url)
                .remark(remark)
                .sortOrder(sortOrder)
                .isActive(isActive)
                .build();
    }

    /**
     * 判断物品是否启用
     *
     * @return true-启用，false-停用
     */
    public boolean isEnabled() {
        return Boolean.TRUE.equals(isActive);
    }

    /**
     * 获取显示名称
     *
     * @return 物品名称
     */
    public String getDisplayName() {
        return name;
    }
}
