package com.inboyu.order.domain.repair.model;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 报修订单附件类型值对象
 */
@Getter
@Builder
@EqualsAndHashCode
public class AttachmentType {
    /**
     * 编码
     */
    String code;
    /**
     * 值
     */
    String value;

    public final static String REPAIR_ATTACHMENT_TYPE_REPAIR_IMAGE = "repair_attachment_type.repair_image"; // 报修图片
    public final static String REPAIR_ATTACHMENT_TYPE_REPAIR_VIDEO = "repair_attachment_type.repair_video"; // 报修视频
    public final static String REPAIR_ATTACHMENT_TYPE_PROGRESS_IMAGE = "repair_attachment_type.progress_image"; // 进度图片
    public final static String REPAIR_ATTACHMENT_TYPE_PROGRESS_VIDEO = "repair_attachment_type.progress_video"; // 进度视频
    public final static String REPAIR_ATTACHMENT_TYPE_SUSPEND_IMAGE = "repair_attachment_type.suspend_image"; // 挂起图片

}