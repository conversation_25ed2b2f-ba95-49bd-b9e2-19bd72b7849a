package com.inboyu.order.domain.repair.model;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 文件类型枚举
 */
@Getter
@Builder
@EqualsAndHashCode
public class FileType {
    /**
     * 编码
     */
    String code;
    /**
     * 值
     */
    String value;

    public final static String REPAIR_FILE_TYPE_IMAGE = "repair_file_type.image"; // 图片
    public final static String REPAIR_FILE_TYPE_VIDEO = "repair_file_type.video"; // 视频
    public final static String REPAIR_FILE_TYPE_DOCUMENT = "repair_file_type.document"; // 文档
}