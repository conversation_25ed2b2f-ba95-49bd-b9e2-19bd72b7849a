package com.inboyu.order.domain.repair.model;

import lombok.Builder;
import lombok.Getter;

/**
 * 标签状态
 */
@Getter
@Builder
public class TagStatus {
    /**
     * 编码
     */
    private final String code;
    /**
     * 值
     */
    private final String value;

    /** 标签状态枚举 */
    public final static String REPAIR_TAG_STATUS_NORMAL = "repair_tag_status.normal"; // 正常
    public final static String REPAIR_TAG_STATUS_OVERTIME = "repair_tag_status.overtime"; // 超时
    public final static String REPAIR_TAG_STATUS_SUSPENDED = "repair_tag_status.suspended"; // 挂单
    public final static String REPAIR_TAG_STATUS_REWORK = "repair_tag_status.rework"; // 返工
    public final static String REPAIR_TAG_STATUS_URGENT = "repair_tag_status.urgent"; // 紧急
    public final static String REPAIR_TAG_STATUS_IMPORTANT = "repair_tag_status.important"; // 重要

    public static TagStatus of(String code) {
        String value = getValueByCode(code);
        return TagStatus.builder().code(code).value(value).build();
    }

    private static String getValueByCode(String code) {
        switch (code) {
            case REPAIR_TAG_STATUS_NORMAL:
                return "正常";
            case REPAIR_TAG_STATUS_OVERTIME:
                return "超时";
            case REPAIR_TAG_STATUS_SUSPENDED:
                return "挂单";
            case REPAIR_TAG_STATUS_REWORK:
                return "返工";
            case REPAIR_TAG_STATUS_URGENT:
                return "紧急";
            case REPAIR_TAG_STATUS_IMPORTANT:
                return "重要";
            default:
                return "未知状态";
        }
    }
}
