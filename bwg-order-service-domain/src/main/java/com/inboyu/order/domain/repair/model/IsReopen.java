package com.inboyu.order.domain.repair.model;

/**
 * 是否重开操作 值对象
 * 按照DDD标准，将ID封装为值对象确保类型安全
 */

public class IsReopen {

    private final Boolean value;

    private IsReopen(Boolean value) {
        if (value == null) {
            throw new IllegalArgumentException("是否重开操作不能为空");
        }
        this.value = value;
    }

    public static IsReopen of(Boolean value) {
        return new IsReopen(value);
    }

    public static IsReopen of(Integer value) {
        if (value == null) {
            throw new IllegalArgumentException("是否重开操作不能为空");
        }
        if (value != 0 && value != 1) {
            throw new IllegalArgumentException("是否重开操作值只能是0或1");
        }
        return new IsReopen(value == 1);
    }

    public Boolean getBooleanValue() {
        return value;
    }

    public Integer getValue() {
        return value ? 1 : 0;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        IsReopen isReopen = (IsReopen) obj;
        return value.equals(isReopen.value);
    }

    @Override
    public int hashCode() {
        return value.hashCode();
    }

}