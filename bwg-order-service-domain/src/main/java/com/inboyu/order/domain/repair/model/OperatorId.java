package com.inboyu.order.domain.repair.model;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 操作者ID值对象
 */
@Getter
@Builder
@EqualsAndHashCode
public class OperatorId {

    /**
     * 操作者ID值
     */
    private final Long value;

    /**
     * 创建操作者ID
     *
     * @param value ID值
     * @return OperatorId
     */
    public static OperatorId of(Long value) {
        return OperatorId.builder().value(value).build();
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }
}
