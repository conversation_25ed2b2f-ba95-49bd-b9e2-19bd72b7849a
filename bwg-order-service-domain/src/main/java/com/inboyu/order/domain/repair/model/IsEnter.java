package com.inboyu.order.domain.repair.model;

import lombok.Builder;
import lombok.Getter;

/**
 * is_enter` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否无人可直接入户 0-否 1-是'
 * 是否无人可直接入户值对象
 * 按照DDD标准，将ID封装为值对象确保类型安全
 */
@Getter
@Builder
public class IsEnter {

    private final Boolean value;

    private IsEnter(Boolean value) {
        if (value == null) {
            throw new IllegalArgumentException("是否无人可直接入户不能为空");
        }
        this.value = value;
    }

    public static IsEnter of(Boolean value) {
        return new IsEnter(value);
    }

    public static IsEnter of(Integer value) {
        if (value == null) {
            throw new IllegalArgumentException("是否无人可直接入户不能为空");
        }
        if (value != 0 && value != 1) {
            throw new IllegalArgumentException("是否无人可直接入户值只能是0或1");
        }
        return new IsEnter(value == 1);
    }

    public Boolean getBooleanValue() {
        return value;
    }

    public Integer getValue() {
        return value ? 1 : 0;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        IsEnter isEnter = (IsEnter) obj;
        return value.equals(isEnter.value);
    }

    @Override
    public int hashCode() {
        return value.hashCode();
    }

    @Override
    public String toString() {
        return "IsEnter{" + "value=" + value + '}';
    }
}