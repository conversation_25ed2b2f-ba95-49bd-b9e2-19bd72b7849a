package com.inboyu.order.domain.repair.service;

import com.inboyu.order.domain.repair.model.RepairOrder;
import com.inboyu.order.domain.repair.model.RepairOrderId;
import com.inboyu.order.domain.repair.model.RepairProgress;
import com.inboyu.order.domain.repair.model.RepairProgressId;

import java.util.List;

/**
 * 报修进度领域服务接口
 * 处理报修进度相关的业务逻辑
 */
public interface RepairProgressDomainService {

    /**
     * 生成报修进度ID
     *
     * @return 报修进度ID
     */
    RepairProgressId generateProgressId();

    /**
     * 创建提交报修进度
     * 当报修订单被提交时自动创建初始进度记录
     *
     * @param repairOrder 报修订单
     */
    void createSubmitProgress(RepairOrder repairOrder);

    /**
     * 获取报修订单进度列表
     *
     * @param orderId 订单ID
     * @return 进度列表
     */
    List<RepairProgress> getRepairOrderProgressList(RepairOrderId orderId);

    /**
     * 创建确认进度
     */
    void createConfirmProgress(RepairOrder repairOrder, String confirmType, String confirmComment, String reworkInstructions);


    /**
     * 创建取消进度记录
     *
     * @param repairOrder  报修订单
     * @param cancelReason 取消原因
     */
    void createCancelProgress(RepairOrder repairOrder, String cancelReason);

    /**
     * 创建受理/不受理进度记录
     *
     * @param repairOrder 报修订单
     * @param operationType 操作类型 (accept/reject)
     * @param operationComment 操作说明
     */
    void createAcceptRejectProgress(RepairOrder repairOrder, String operationType, String operationComment);

    /**
     * 创建完成维修进度记录
     *
     * @param repairOrder 报修订单
     * @param maintainerName 维修人员姓名
     * @param completeComment 完成说明
     */
    void createCompleteProgress(RepairOrder repairOrder, String maintainerName, String completeComment);
}
