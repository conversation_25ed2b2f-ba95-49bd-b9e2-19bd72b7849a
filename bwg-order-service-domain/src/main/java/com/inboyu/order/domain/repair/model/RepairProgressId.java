package com.inboyu.order.domain.repair.model;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 报修订单进度ID值对象
 * 按照DDD标准，将ID封装为值对象确保类型安全
 */
@Getter
@Builder
@EqualsAndHashCode
public class RepairProgressId {

    private final Long value;

    private RepairProgressId(Long value) {
        if (value == null || value <= 0) {
            throw new IllegalArgumentException("报修订单进度ID不能为空或小于等于0");
        }
        this.value = value;
    }

    public static RepairProgressId of(Long value) {

        return new RepairProgressId(value);
    }

    public static RepairProgressId of(String value) {
        return new RepairProgressId(Long.valueOf(value));
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }
}
