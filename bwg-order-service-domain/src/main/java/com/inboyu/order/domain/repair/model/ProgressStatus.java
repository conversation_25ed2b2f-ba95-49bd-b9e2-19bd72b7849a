package com.inboyu.order.domain.repair.model;

import lombok.Builder;
import lombok.Getter;

/**
 * 进度状态枚举
 */
@Getter
@Builder
public class ProgressStatus {
    /**
     * 编码
     */
    private final String code;
    /**
     * 值
     */
    private final String value;

    /** 进度状态常量 */
    public final static String REPAIR_PROGRESS_STATUS_SUBMITTED = "repair_progress_status.submitted"; // 已提交
    public final static String REPAIR_PROGRESS_STATUS_ACCEPTED = "repair_progress_status.accepted"; // 已受理
    public final static String REPAIR_PROGRESS_STATUS_PROCESSING = "repair_progress_status.processing"; // 处理中
    public final static String REPAIR_PROGRESS_STATUS_COMPLETED = "repair_progress_status.completed"; // 已完成
    public final static String REPAIR_PROGRESS_STATUS_CLOSED = "repair_progress_status.closed"; // 已关闭

    /**
     * 已提交状态
     */
    public static ProgressStatus submitted() {
        return ProgressStatus.builder()
                .code(REPAIR_PROGRESS_STATUS_SUBMITTED)
                .value("已提交")
                .build();
    }

    /**
     * 已受理状态
     */
    public static ProgressStatus accepted() {
        return ProgressStatus.builder()
                .code(REPAIR_PROGRESS_STATUS_ACCEPTED)
                .value("已受理")
                .build();
    }

    /**
     * 处理中状态
     */
    public static ProgressStatus processing() {
        return ProgressStatus.builder()
                .code(REPAIR_PROGRESS_STATUS_PROCESSING)
                .value("处理中")
                .build();
    }

    /**
     * 已完成状态
     */
    public static ProgressStatus completed() {
        return ProgressStatus.builder()
                .code(REPAIR_PROGRESS_STATUS_COMPLETED)
                .value("已完成")
                .build();
    }

    /**
     * 已关闭状态
     */
    public static ProgressStatus closed() {
        return ProgressStatus.builder()
                .code(REPAIR_PROGRESS_STATUS_CLOSED)
                .value("已关闭")
                .build();
    }

    /**
     * 已拒绝状态
     */
    public static ProgressStatus rejected() {
        return ProgressStatus.builder()
                .code("repair_progress_status.rejected")
                .value("已拒绝")
                .build();
    }

    /**
     * 已确认状态
     */
    public static ProgressStatus confirmed() {
        return ProgressStatus.builder()
                .code("repair_progress_status.confirmed")
                .value("已确认")
                .build();
    }

    /**
     * 已重开状态
     */
    public static ProgressStatus reopened() {
        return ProgressStatus.builder()
                .code("repair_progress_status.reopened")
                .value("已重开")
                .build();
    }

    /**
     * 已取消状态
     */
    public static ProgressStatus cancelled() {
        return ProgressStatus.builder()
                .code("repair_progress_status.cancelled")
                .value("已取消")
                .build();
    }

    public static ProgressStatus of(String code) {
        switch (code) {
            case REPAIR_PROGRESS_STATUS_SUBMITTED:
                return submitted();
            case REPAIR_PROGRESS_STATUS_ACCEPTED:
                return accepted();
            case REPAIR_PROGRESS_STATUS_PROCESSING:
                return processing();
            case REPAIR_PROGRESS_STATUS_COMPLETED:
                return completed();
            case REPAIR_PROGRESS_STATUS_CLOSED:
                return closed();
            default:
                return ProgressStatus.builder().code(code).value("未知状态").build();
        }
    }
}
