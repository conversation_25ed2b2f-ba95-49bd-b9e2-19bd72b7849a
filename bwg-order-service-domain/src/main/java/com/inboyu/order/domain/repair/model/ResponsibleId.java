package com.inboyu.order.domain.repair.model;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 责任方ID值对象
 */
@Getter
@Builder
@EqualsAndHashCode
public class ResponsibleId {

    /**
     * 责任方ID值
     */
    private final Long value;

    /**
     * 创建责任方ID
     *
     * @param value ID值
     * @return ResponsibleId
     */
    public static ResponsibleId of(Long value) {
        return ResponsibleId.builder().value(value).build();
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }
}
