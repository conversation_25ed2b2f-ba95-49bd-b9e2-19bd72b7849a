package com.inboyu.order.domain.repair.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 报修服务日值对象
 * 封装报修服务的星期几配置
 */
@Getter
@EqualsAndHashCode
public class RepairWeekDay {

    private final String code;
    private final String title;

    private RepairWeekDay(String code, String title) {
        if (code == null || code.trim().isEmpty()) {
            throw new IllegalArgumentException("报修服务日编码不能为空");
        }
        if (title == null || title.trim().isEmpty()) {
            throw new IllegalArgumentException("报修服务日标题不能为空");
        }
        this.code = code;
        this.title = title;
    }

    public static RepairWeekDay create(String code, String title) {
        return new RepairWeekDay(code, title);
    }

    // 预定义的常量
    public static final RepairWeekDay MONDAY = new RepairWeekDay("repair_config_repair_week_day.Monday", "周一");
    public static final RepairWeekDay TUESDAY = new RepairWeekDay("repair_config_repair_week_day.Tuesday", "周二");
    public static final RepairWeekDay WEDNESDAY = new RepairWeekDay("repair_config_repair_week_day.Wednesday", "周三");
    public static final RepairWeekDay THURSDAY = new RepairWeekDay("repair_config_repair_week_day.Thursday", "周四");
    public static final RepairWeekDay FRIDAY = new RepairWeekDay("repair_config_repair_week_day.Friday", "周五");
    public static final RepairWeekDay SATURDAY = new RepairWeekDay("repair_config_repair_week_day.Saturday", "周六");
    public static final RepairWeekDay SUNDAY = new RepairWeekDay("repair_config_repair_week_day.Sunday", "周日");

    @Override
    public String toString() {
        return title;
    }
}
