package com.inboyu.order.domain.repair.service.impl;

import com.inboyu.order.domain.repair.repository.RepairLogRepository;
import com.inboyu.order.domain.repair.service.RepairLogDomainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class RepairLogDomainServiceImpl  implements RepairLogDomainService {
    @Autowired
    private RepairLogRepository repairLogRepository;
    @Override
    public void save(String key, String content) {
        repairLogRepository.save(key, content);
    }
}
