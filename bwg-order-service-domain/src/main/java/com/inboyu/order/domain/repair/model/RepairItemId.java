package com.inboyu.order.domain.repair.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 报修物品ID值对象
 */
@Getter
@EqualsAndHashCode
public class RepairItemId {

    private final Long value;

    private RepairItemId(Long value) {
        if (value == null || value <= 0) {
            throw new IllegalArgumentException("报修物品ID不能为空或小于等于0");
        }
        this.value = value;
    }

    public static RepairItemId of(Long value) {
        return new RepairItemId(value);
    }

    public static RepairItemId of(String value) {
        return new RepairItemId(Long.valueOf(value));
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }
}
