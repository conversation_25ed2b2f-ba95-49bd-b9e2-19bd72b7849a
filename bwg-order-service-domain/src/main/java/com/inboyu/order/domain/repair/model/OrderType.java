package com.inboyu.order.domain.repair.model;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 报修类型
 * repair_order_type.resident 在住报修
 * repair_order_type.vacant 空房报修
 * repair_order_type.checkout 退房报修
 */
@Getter
@Builder
@EqualsAndHashCode
public class OrderType {
    /**
     * 编码
     */
    private final String code;
    /**
     * 值
     */
    private final String value;

    /**
     * 报修类型枚举
     */
    public final static String REPAIR_ORDER_TYPE_RESIDENT = "repair_order_type.resident";//在住报修
    public final static String REPAIR_ORDER_TYPE_VACANT = "repair_order_type.vacant";//空房报修
    public final static String REPAIR_ORDER_TYPE_CHECKOUT = "repair_order_type.checkout";//退房报修

    public static OrderType of(String code) {
        String value = getValueByCode(code);
        return OrderType.builder().code(code).value(value).build();
    }

    private static String getValueByCode(String code) {
        switch (code) {
            case REPAIR_ORDER_TYPE_RESIDENT:
                return "在住报修";
            case REPAIR_ORDER_TYPE_VACANT:
                return "空房报修";
            case REPAIR_ORDER_TYPE_CHECKOUT:
                return "退房报修";
            default:
                return "未知类型";
        }
    }
}
