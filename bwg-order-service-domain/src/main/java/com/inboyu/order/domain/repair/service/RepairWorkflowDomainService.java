package com.inboyu.order.domain.repair.service;

import java.util.Map;

/**
 * 报修工作流领域服务接口
 * 定义报修工作流相关的业务逻辑
 */
public interface RepairWorkflowDomainService {

    /**
     * 创建报修工单工作流
     *
     * @param repairOrderId 报修工单ID
     * @param title 标题
     * @param description 描述
     * @param userId 用户ID
     * @param userName 用户名
     * @param storeId 门店ID
     * @return 工作流实例ID
     */
    String createRepairOrderWorkflow(String repairOrderId, String title, String description, 
                                   String userId, String userName, String storeId);

    /**
     * 处理工作流回调通知
     *
     * @param instanceId 实例ID
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @param instanceStatus 实例状态
     * @param nodeType 节点类型
     * @param isEnd 是否结束
     * @param isChangeInstance 是否变更实例
     * @param sysRemark 系统备注
     * @param variable 业务变量
     * @param form 表单数据
     * @return 处理结果
     */
    String handleWorkflowCallback(String instanceId, String businessId, String businessType,
                                String instanceStatus, String nodeType, Boolean isEnd,
                                Boolean isChangeInstance, String sysRemark,
                                Map<String, String> variable, Map<String, Object> form);

    /**
     * 取消工作流
     *
     * @param instanceId 工作流实例ID
     * @param userName 用户名
     * @param userType 用户类型
     * @param userId 用户ID
     * @param comment 备注
     * @return 取消结果
     */
    String cancelWorkflow(String instanceId, String userName, String userType, String userId, String comment);

    /**
     * 工作流审批
     *
     * @param instanceStepId 实例步骤ID
     * @param operateType 操作类型
     * @param comment 备注
     * @param userType 用户类型
     * @param userId 用户ID
     * @param userName 用户名
     * @return 审批结果
     */
    String workflowApproval(String instanceStepId, String operateType, String comment,
                          String userType, String userId, String userName);

    /**
     * 查询工作流实例详情
     *
     * @param workflowInstanceId 工作流实例ID
     * @return 工作流实例详情JSON字符串
     */
    String getWorkflowInstance(String workflowInstanceId);
}
