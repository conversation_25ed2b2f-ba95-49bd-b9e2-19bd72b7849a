package com.inboyu.order.domain.repair.model;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 报修订单ID值对象
 */
@Getter
@Builder
@EqualsAndHashCode
public class RepairOrderId {

    /**
     * 报修订单ID值
     */
    private final Long value;

    /**
     * 创建报修订单ID
     *
     * @param value ID值
     * @return RepairOrderId
     */
    public static RepairOrderId of(Long value) {

        return RepairOrderId.builder().value(value).build();
    }

    public static RepairOrderId of(String value) {

        return RepairOrderId.builder().value(Long.valueOf(value)).build();
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }
}
