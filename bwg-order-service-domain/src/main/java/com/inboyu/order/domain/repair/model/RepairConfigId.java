package com.inboyu.order.domain.repair.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 报修配置ID值对象
 * 按照DDD标准，将ID封装为值对象确保类型安全
 */
@Getter
@EqualsAndHashCode
public class RepairConfigId {

    private final Long value;

    private RepairConfigId(Long value) {
        if (value == null || value <= 0) {
            throw new IllegalArgumentException("报修配置ID不能为空或小于等于0");
        }
        this.value = value;
    }

    public static RepairConfigId of(Long value) {
        return new RepairConfigId(value);
    }

    public static RepairConfigId of(String value) {
        return new RepairConfigId(Long.valueOf(value));
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }
}
