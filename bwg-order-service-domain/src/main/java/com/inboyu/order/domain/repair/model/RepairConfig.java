package com.inboyu.order.domain.repair.model;

import lombok.Builder;
import lombok.Getter;

import java.time.LocalTime;
import java.util.List;

/**
 * 报修配置领域对象
 * 包含门店报修配置的基本信息和业务行为
 */
@Getter
@Builder
public class RepairConfig {

    private final RepairConfigId id;
    private final StoreId storeId;
    private final LocalTime repairStartTime;
    private final LocalTime repairEndTime;
    private final Integer repairTimeInterval;
    private final String repairWeekDay;
    private final Integer maxRepairDays;
    private final RepairTodayEnable repairTodayEnable;
    private final List<RepairItemId> repairItemIds;

    /**
     * 创建报修配置
     *
     * @param id                报修配置ID
     * @param storeId           门店ID
     * @param repairStartTime   报修开始时间
     * @param repairEndTime     报修结束时间
     * @param repairTimeInterval 报修时段时长
     * @param repairWeekDay     每周服务日
     * @param maxRepairDays     最远可预约日期
     * @param repairTodayEnable 是否开放当天预约
     * @param repairItemIds     可报修物品ID列表
     * @return 报修配置对象
     */
    public static RepairConfig create(RepairConfigId id, StoreId storeId, LocalTime repairStartTime,
                                     LocalTime repairEndTime, Integer repairTimeInterval, String repairWeekDay,
                                     Integer maxRepairDays, RepairTodayEnable repairTodayEnable,
                                     List<RepairItemId> repairItemIds) {
        return RepairConfig.builder()
                .id(id)
                .storeId(storeId)
                .repairStartTime(repairStartTime)
                .repairEndTime(repairEndTime)
                .repairTimeInterval(repairTimeInterval)
                .repairWeekDay(repairWeekDay)
                .maxRepairDays(maxRepairDays)
                .repairTodayEnable(repairTodayEnable)
                .repairItemIds(repairItemIds)
                .build();
    }

    /**
     * 判断是否开放当天预约
     *
     * @return true-开放，false-不开放
     */
    public boolean isTodayEnabled() {
        return RepairTodayEnable.ENABLED.equals(repairTodayEnable);
    }

    /**
     * 获取报修时长（分钟）
     *
     * @return 报修时长
     */
    public Integer getRepairDurationMinutes() {
        return repairTimeInterval;
    }

    /**
     * 验证报修时间配置是否合理
     *
     * @return true-合理，false-不合理
     */
    public boolean isTimeConfigValid() {
        if (repairStartTime == null || repairEndTime == null) {
            return false;
        }
        return repairStartTime.isBefore(repairEndTime);
    }
}
