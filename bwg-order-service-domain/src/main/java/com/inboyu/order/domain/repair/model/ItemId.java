package com.inboyu.order.domain.repair.model;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 物品ID值对象
 */
@Getter
@Builder
@EqualsAndHashCode
public class ItemId {

    /**
     * 物品ID值
     */
    private final Long value;

    /**
     * 创建物品ID
     *
     * @param value ID值
     * @return ItemId
     */
    public static ItemId of(Long value) {
        return ItemId.builder().value(value).build();
    }

    public static ItemId of(String value) {
        return ItemId.builder().value(Long.valueOf(value)).build();
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }
}
