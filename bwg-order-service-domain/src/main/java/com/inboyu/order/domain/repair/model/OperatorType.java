package com.inboyu.order.domain.repair.model;

import lombok.Builder;
import lombok.Getter;

/**
 * 操作者类型枚举
 */
@Getter
@Builder
public class OperatorType {
    /**
     * 编码
     */
    private final String code;
    /**
     * 值
     */
    private final String value;

    /** 操作者类型常量 */
    public final static String REPAIR_OPERATOR_TYPE_TENANT = "repair_operator_type.tenant"; // 租户
    public final static String REPAIR_OPERATOR_TYPE_STORE = "repair_operator_type.store"; // 门店
    public final static String REPAIR_OPERATOR_TYPE_MAINTAINER = "repair_operator_type.maintainer"; // 维修工

    /**
     * 租户类型
     */
    public static OperatorType tenant() {
        return OperatorType.builder()
                .code(REPAIR_OPERATOR_TYPE_TENANT)
                .value("租户")
                .build();
    }

    /**
     * 门店类型
     */
    public static OperatorType store() {
        return OperatorType.builder()
                .code(REPAIR_OPERATOR_TYPE_STORE)
                .value("门店")
                .build();
    }

    /**
     * 维修工类型
     */
    public static OperatorType maintainer() {
        return OperatorType.builder()
                .code(REPAIR_OPERATOR_TYPE_MAINTAINER)
                .value("维修工")
                .build();
    }


    public static OperatorType of(String code) {
        switch (code) {
            case REPAIR_OPERATOR_TYPE_TENANT:
                return tenant();
            case REPAIR_OPERATOR_TYPE_STORE:
                return store();
            case REPAIR_OPERATOR_TYPE_MAINTAINER:
                return maintainer();
            default:
                return OperatorType.builder().code(code).value("未知类型").build();
        }
    }
}
