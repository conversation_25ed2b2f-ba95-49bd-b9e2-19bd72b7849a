package com.inboyu.order.domain.repair.service;

import com.inboyu.order.domain.repair.model.RepairOrder;
import com.inboyu.order.domain.repair.model.RepairOrderId;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;

import java.util.List;

public interface RepairOrderDomainService {

    /**
     * 生成报修订单ID
     *
     * @return 报修订单ID
     */
    RepairOrderId generateId();


    /**
     * 创建报修订单
     *
     * @param repairOrder 报修订单
     * @return 创建后的报修订单
     */
    RepairOrder createRepairOrder(RepairOrder repairOrder);

    /**
     * 批量创建报修订单
     *
     * @param repairOrders 报修订单列表
     * @return 创建后的报修订单列表
     */
    List<RepairOrder> batchCreateRepairOrders(List<RepairOrder> repairOrders);

    /**
     * 获取报修订单详情
     *
     * @param orderId 订单ID
     * @return 报修订单详情
     */
    RepairOrder getRepairOrderDetail(RepairOrderId orderId);

    /**
     * 取消报修订单
     *
     * @param orderId 订单ID
     * @param cancelReason 取消原因
     * @return 取消后的报修订单
     */
    RepairOrder cancelRepairOrder(RepairOrderId orderId, String cancelReason);

    /**
     * 确认报修订单
     *
     * @param orderId 订单ID
     * @param confirmType 确认类型（confirm/rework）
     * @param confirmComment 确认备注
     * @param reworkInstructions 返工说明
     * @return 确认后的报修订单
     */
    RepairOrder confirmRepairOrder(RepairOrderId orderId, String confirmType, String confirmComment, String reworkInstructions);

    /**
     * 分页查询报修订单列表
     *
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param customerId 客户ID
     * @param storeId 门店ID
     * @param status 状态
     * @param responsibleId 负责人ID
     * @param roomNum 房号
     * @param roomId 房间ID
     * @return 分页结果
     */
    Pagination<RepairOrder> pageRepairOrder(
            Integer pageNum, Integer pageSize,
            String customerId, String storeId, String status,
            String responsibleId, String roomNum, String roomId);


    /**
     * 受理报修订单
     *
     * @param orderId 订单ID
     * @param operatorId 操作人ID
     * @param operationComment 操作说明
     * @return 受理后的报修订单
     */
    RepairOrder acceptRepairOrder(RepairOrderId orderId, Long operatorId, String operationComment);

    /**
     * 不受理报修订单
     *
     * @param orderId 订单ID
     * @param operatorId 操作人ID
     * @param operationComment 操作说明
     * @return 不受理后的报修订单
     */
    RepairOrder rejectRepairOrder(RepairOrderId orderId, Long operatorId, String operationComment);

    /**
     * 完成维修
     *
     * @param orderId 订单ID
     * @param maintainerId 维修人员ID
     * @param completeComment 完成说明
     * @param actualCompleteTime 实际完成时间
     * @return 完成后的报修订单
     */
    RepairOrder completeRepairOrder(RepairOrderId orderId, Long maintainerId, String completeComment, String actualCompleteTime);
}
