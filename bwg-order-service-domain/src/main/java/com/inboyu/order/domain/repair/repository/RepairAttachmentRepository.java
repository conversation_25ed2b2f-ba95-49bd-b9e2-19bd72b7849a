package com.inboyu.order.domain.repair.repository;

import com.inboyu.order.domain.repair.model.RepairAttachment;
import com.inboyu.order.domain.repair.model.RepairOrderId;
import com.inboyu.order.domain.repair.model.AttachmentType;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 报修附件仓储接口
 */
public interface RepairAttachmentRepository {

    /**
     * 根据附件ID查询
     */
    Optional<RepairAttachment> findById(Long attachmentId);

    /**
     * 根据报修工单ID查询附件列表
     */
    List<RepairAttachment> findByRepairOrderId(RepairOrderId repairOrderId);

    /**
     * 根据附件类型查询附件列表
     */
    List<RepairAttachment> findByAttachmentType(AttachmentType attachmentType);

    /**
     * 根据文件类型查询附件列表
     */
    List<RepairAttachment> findByFileType(Integer fileType);

    /**
     * 根据上传时间范围查询附件列表
     */
    List<RepairAttachment> findByUploadTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据报修工单ID和附件类型查询附件列表
     */
    List<RepairAttachment> findByRepairOrderIdAndAttachmentType(RepairOrderId repairOrderId, AttachmentType attachmentType);

    /**
     * 根据报修工单ID和文件类型查询附件列表
     */
    List<RepairAttachment> findByRepairOrderIdAndFileType(RepairOrderId repairOrderId, Integer fileType);

    /**
     * 根据文件名模糊查询附件列表
     */
    List<RepairAttachment> findByFileNameContaining(String fileName);

    /**
     * 分页查询附件记录
     */
    List<RepairAttachment> findByRepairOrderIdAndFilters(RepairOrderId repairOrderId, String filterBy, String filter,
                                                        Integer pageNum, Integer pageSize);

    /**
     * 统计附件数量
     */
    long countByRepairOrderId(RepairOrderId repairOrderId);

    /**
     * 统计指定类型的附件数量
     */
    long countByRepairOrderIdAndAttachmentType(RepairOrderId repairOrderId, AttachmentType attachmentType);

    /**
     * 统计指定文件类型的附件数量
     */
    long countByRepairOrderIdAndFileType(RepairOrderId repairOrderId, Integer fileType);

    /**
     * 保存附件记录
     */
    RepairAttachment save(RepairAttachment repairAttachment);

    /**
     * 批量保存附件记录
     */
    List<RepairAttachment> saveAll(List<RepairAttachment> repairAttachments);

    /**
     * 更新附件记录
     */
    RepairAttachment update(RepairAttachment repairAttachment);

    /**
     * 根据ID删除附件记录
     */
    void deleteById(Long attachmentId);

    /**
     * 根据报修工单ID删除所有附件记录
     */
    void deleteByRepairOrderId(RepairOrderId repairOrderId);

    /**
     * 根据附件类型删除附件记录
     */
    void deleteByRepairOrderIdAndAttachmentType(RepairOrderId repairOrderId, AttachmentType attachmentType);

    /**
     * 更新附件文件名
     */
    void updateFileNameById(Long attachmentId, String fileName);

    /**
     * 更新附件文件URL
     */
    void updateFileUrlById(Long attachmentId, String fileUrl);
}
