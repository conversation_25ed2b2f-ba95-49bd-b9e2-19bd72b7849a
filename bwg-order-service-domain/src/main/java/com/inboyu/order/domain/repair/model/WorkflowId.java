package com.inboyu.order.domain.repair.model;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 工作流ID值对象
 * 按照DDD标准，将ID封装为值对象确保类型安全
 */
@Getter
@Builder
@EqualsAndHashCode
public class WorkflowId {

    private final String value;

    private WorkflowId(String value) {
        this.value = value;
    }

    /**
     * 创建工作流ID
     *
     * @param value ID值
     * @return WorkflowId
     */
    public static WorkflowId of(String value) {
        return new WorkflowId(value);
    }

    /**
     * 从Long类型创建工作流ID
     *
     * @param value Long类型ID值
     * @return WorkflowId
     */
    public static WorkflowId of(Long value) {
        if (value == null || value <= 0) {
            throw new IllegalArgumentException("工作流ID不能为空或小于等于0");
        }
        return new WorkflowId(String.valueOf(value));
    }

    @Override
    public String toString() {
        return value;
    }
}
