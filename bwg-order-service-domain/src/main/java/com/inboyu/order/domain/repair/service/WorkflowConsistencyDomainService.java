package com.inboyu.order.domain.repair.service;

import com.inboyu.order.domain.repair.model.RepairOrder;
import com.inboyu.order.domain.repair.model.RepairOrderId;
import com.inboyu.order.domain.repair.model.RepairOrderStatus;

/**
 * 工作流状态一致性领域服务
 * 负责确保报修订单状态与工作流状态的一致性
 * 遵循DDD原则，封装业务规则
 */
public interface WorkflowConsistencyDomainService {

    /**
     * 验证报修订单状态与工作流状态的一致性
     * 
     * @param repairOrder 报修订单
     * @param workflowStatus 工作流状态
     * @param nodeType 节点类型
     * @return 是否一致
     */
    boolean validateStatusConsistency(RepairOrder repairOrder, String workflowStatus, String nodeType);

    /**
     * 根据工作流状态更新报修订单状态
     * 确保状态转换的合法性
     * 
     * @param repairOrder 报修订单
     * @param workflowStatus 工作流状态
     * @param nodeType 节点类型
     * @return 更新后的报修订单
     */
    RepairOrder updateOrderStatusByWorkflow(RepairOrder repairOrder, String workflowStatus, String nodeType);

    /**
     * 检查状态转换是否合法
     * 
     * @param currentStatus 当前状态
     * @param targetStatus 目标状态
     * @return 是否合法
     */
    boolean isValidStatusTransition(String currentStatus, String targetStatus);

    /**
     * 获取状态转换规则
     * 
     * @param currentStatus 当前状态
     * @return 允许的下一个状态列表
     */
    String[] getAllowedNextStatuses(String currentStatus);

    /**
     * 记录状态不一致的异常情况
     * 
     * @param repairOrderId 报修订单ID
     * @param orderStatus 订单状态
     * @param workflowStatus 工作流状态
     * @param nodeType 节点类型
     */
    void recordStatusInconsistency(RepairOrderId repairOrderId, String orderStatus, String workflowStatus, String nodeType);
}
