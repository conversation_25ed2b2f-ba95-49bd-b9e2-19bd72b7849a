package com.inboyu.order.domain.repair.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 门店ID值对象
 * 按照DDD标准，将ID封装为值对象确保类型安全
 */
@Getter
@EqualsAndHashCode
public class StoreId {

    private final Long value;

    private StoreId(Long value) {
        if (value == null || value <= 0) {
            throw new IllegalArgumentException("门店ID不能为空或小于等于0");
        }
        this.value = value;
    }

    public static StoreId of(Long value) {
        return new StoreId(value);
    }

    public static StoreId of(String value) {
        return new StoreId(Long.valueOf(value));
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }
}
