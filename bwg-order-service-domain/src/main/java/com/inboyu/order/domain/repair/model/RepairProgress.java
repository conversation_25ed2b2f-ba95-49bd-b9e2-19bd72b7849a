package com.inboyu.order.domain.repair.model;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 报修进度领域模型（实体）
 */
@Data
@Builder
public class RepairProgress {
    private Long id; // 自增ID，无业务含义
    private LocalDateTime createTime; // 记录创建时间
    private LocalDateTime modifyTime; // 记录修改时间
    private Long version; // 记录版本号（乐观锁）
    private Boolean deleted; // 是否删除，0否，1是
    
    private Long progressId; // 进度业务ID
    private RepairOrderId repairOrderId; // 报修工单ID（使用值对象）
    private Long userId; // 操作用户ID
    private String userName; // 操作用户名
    private String detail; // 进度描述
    private ProgressType type; // 操作者类型（使用值对象）
    private RepairOrderStatus status; // 处理状态（使用值对象）
    private String comment; // 交接、完成备注
    private String distributeReason; // 派单原因
    private String distributeDetail; // 派单标签
    private Boolean isReopen; // 是否重开操作
    private String reopenReason; // 重开原因
    private Integer processMethod; // 处理方式：0-自受理 1-转派
    private Long assigneeId; // 转派目标处理人ID
    private String assigneeName; // 转派目标处理人姓名
    
    /**
     * 创建进度记录
     */
    public static RepairProgress create(RepairOrderId repairOrderId, Long userId, String userName, 
                                      String detail, ProgressType type, RepairOrderStatus status, 
                                      String comment) {
        return RepairProgress.builder()
                .repairOrderId(repairOrderId)
                .userId(userId)
                .userName(userName)
                .detail(detail)
                .type(type)
                .status(status)
                .comment(comment)
                .createTime(LocalDateTime.now())
                .deleted(false)
                .version(0L)
                .build();
    }
    
    /**
     * 创建转派进度记录
     */
    public static RepairProgress createTransfer(RepairOrderId repairOrderId, Long userId, String userName,
                                              Long assigneeId, String assigneeName, String reason) {
        return RepairProgress.builder()
                .repairOrderId(repairOrderId)
                .userId(userId)
                .userName(userName)
                .detail("工单已转派给 " + assigneeName)
                .type(ProgressType.MANAGER)
                .status(RepairOrderStatus.PROCESSING)
                .distributeReason(reason)
                .processMethod(1) // 转派
                .assigneeId(assigneeId)
                .assigneeName(assigneeName)
                .createTime(LocalDateTime.now())
                .deleted(false)
                .version(0L)
                .build();
    }
    
    /**
     * 创建重开进度记录
     */
    public static RepairProgress createReopen(RepairOrderId repairOrderId, Long userId, String userName,
                                            String reason) {
        return RepairProgress.builder()
                .repairOrderId(repairOrderId)
                .userId(userId)
                .userName(userName)
                .detail("工单已重开")
                .type(ProgressType.SYSTEM)
                .status(RepairOrderStatus.PROCESSING)
                .isReopen(true)
                .reopenReason(reason)
                .createTime(LocalDateTime.now())
                .deleted(false)
                .version(0L)
                .build();
    }
}
