package com.inboyu.order.domain.repair.model;

import lombok.Builder;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * 报修订单处理进度领域对象
 */
@Getter
@Builder
public class RepairProgress {
    /**
     * 进度业务ID
     */
    private final RepairProgressId progressId;
    /**
     * 报修工单ID
     */
    private final RepairOrderId repairOrderId;
    /**
     * 操作用户ID
     */
    private final OperatorId operatorId;
    /**
     * 操作用户名
     */
    private final String operatorName;
    /**
     * 进度描述
     */
    private final String detail;
    /**
     * 进度副标题
     */
    private final String subDetail;
    /**
     * 操作者类型,字典维护
     */
    private final OperatorType operatorType;
    /**
     * 处理状态,字典维护
     */
    private final ProgressStatus status;
    /**
     * 交接、完成备注
     */
    private final String comment;
    /**
     * 是否重开操作 0-否 1-是
     */
    private final IsReopen isReopen;
    /**
     * 重开原因
     */
    private final String reopenReason;

    /**
     * 创建时间
     */
    private final LocalDateTime createTime;

    /**
     * 修改时间
     */
    private final LocalDateTime modifyTime;

    /**
     * 创建提交报修进度
     */
    public static RepairProgress createSubmitProgress(RepairProgressId progressId, RepairOrder repairOrder) {
        // 构建子详情：{item_id-item_tag} {detail}
        String subDetail = String.format("{%s-%s} %s",
                repairOrder.getRepairItemId().getValue(),
                repairOrder.getItemTag(),
                repairOrder.getDetail());

        return RepairProgress.builder()
                .progressId(progressId)
                .repairOrderId(repairOrder.getRepairOrderId())
                .operatorId(OperatorId.of(0L))
                .operatorName("")
                .detail("您提交了报修，请等待门店受理")
                .subDetail(subDetail)
                .operatorType(OperatorType.tenant())
                .status(ProgressStatus.submitted())
                .comment("")
                .isReopen(IsReopen.of(0))
                .reopenReason("")
                .createTime(LocalDateTime.now())
                .modifyTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建取消报修进度
     */
    public static RepairProgress createCancelProgress(RepairProgressId progressId, RepairOrder repairOrder, String cancelReason) {
        return RepairProgress.builder()
                .progressId(progressId)
                .repairOrderId(repairOrder.getRepairOrderId())
                .operatorId(OperatorId.of(0L))
                .operatorName("客户")
                .detail("您取消了报修")
                .subDetail("取消原因：" + cancelReason)
                .operatorType(OperatorType.tenant())
                .status(ProgressStatus.cancelled())
                .comment(cancelReason)
                .isReopen(IsReopen.of(0))
                .reopenReason("")
                .createTime(LocalDateTime.now())
                .modifyTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建确认进度
     */
    public static RepairProgress createConfirmProgress(RepairProgressId progressId, RepairOrder repairOrder, String confirmType, String confirmComment, String reworkInstructions) {
        String detail;
        String subDetail;
        ProgressStatus status;
        String comment;
        IsReopen isReopen;
        String reopenReason;

        if ("confirm".equals(confirmType)) {
            detail = "您确认了维修完成";
            subDetail = "维修结果满意";
            status = ProgressStatus.confirmed();
            comment = confirmComment;
            isReopen = IsReopen.of(0);
            reopenReason = "";
        } else {
            detail = "您请求返工";
            subDetail = "返工说明：" + reworkInstructions;
            status = ProgressStatus.reopened();
            comment = reworkInstructions;
            isReopen = IsReopen.of(1);
            reopenReason = reworkInstructions;
        }

        return RepairProgress.builder()
                .progressId(progressId)
                .repairOrderId(repairOrder.getRepairOrderId())
                .operatorId(OperatorId.of(0L))
                .operatorName("客户")
                .detail(detail)
                .subDetail(subDetail)
                .operatorType(OperatorType.tenant())
                .status(status)
                .comment(comment)
                .isReopen(isReopen)
                .reopenReason(reopenReason)
                .createTime(LocalDateTime.now())
                .modifyTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建受理/不受理进度
     */
    public static RepairProgress createAcceptRejectProgress(RepairProgressId progressId, RepairOrder repairOrder, String operationType, String operationComment) {
        String detail;
        String subDetail;
        ProgressStatus status;
        String comment;
        IsReopen isReopen;
        String reopenReason;

        if ("accept".equals(operationType)) {
            detail = "运营端已受理报修";
            subDetail = "已安排维修人员处理";
            status = ProgressStatus.accepted();
            comment = operationComment;
            isReopen = IsReopen.of(0);
            reopenReason = "";
        } else {
            detail = "运营端不受理报修";
            subDetail = "不符合受理条件";
            status = ProgressStatus.rejected();
            comment = operationComment;
            isReopen = IsReopen.of(0);
            reopenReason = "";
        }

        return RepairProgress.builder()
                .progressId(progressId)
                .repairOrderId(repairOrder.getRepairOrderId())
                .operatorId(OperatorId.of(0L))
                .operatorName("运营端")
                .detail(detail)
                .subDetail(subDetail)
                .operatorType(OperatorType.maintainer())
                .status(status)
                .comment(comment)
                .isReopen(isReopen)
                .reopenReason(reopenReason)
                .createTime(LocalDateTime.now())
                .modifyTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建完成维修进度
     */
    public static RepairProgress createCompleteProgress(RepairProgressId progressId, RepairOrder repairOrder, String maintainerName, String completeComment) {
        return RepairProgress.builder()
                .progressId(progressId)
                .repairOrderId(repairOrder.getRepairOrderId())
                .operatorId(OperatorId.of(0L))
                .operatorName(maintainerName)
                .detail("维修已完成")
                .subDetail("等待客户确认")
                .operatorType(OperatorType.maintainer())
                .status(ProgressStatus.completed())
                .comment(completeComment)
                .isReopen(IsReopen.of(0))
                .reopenReason("")
                .createTime(LocalDateTime.now())
                .modifyTime(LocalDateTime.now())
                .build();
    }
}