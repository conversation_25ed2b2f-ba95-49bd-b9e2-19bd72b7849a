package com.inboyu.order.domain.repair.service;

import com.inboyu.order.domain.repair.model.AttachmentId;
import com.inboyu.order.domain.repair.model.RepairAttachment;
import com.inboyu.order.domain.repair.model.RepairOrderId;

import java.util.List;

/**
 * 报修附件领域服务接口
 * 处理报修附件相关的业务逻辑
 */
public interface RepairAttachmentDomainService {

    /**
     * 生成附件ID
     *
     * @return 附件ID
     */
    AttachmentId generateAttachmentId();

    /**
     * 批量创建报修附件
     * 验证附件业务规则并创建附件记录
     *
     * @param attachments 附件列表
     * @param repairOrderId 报修订单ID
     * @return 创建后的附件列表
     */
    List<RepairAttachment> batchCreateAttachments(List<RepairAttachment> attachments, RepairOrderId repairOrderId);

    /**
     * 创建单个报修附件
     *
     * @param attachment 附件对象
     * @param repairOrderId 报修订单ID
     * @return 创建后的附件
     */
    RepairAttachment createAttachment(RepairAttachment attachment, RepairOrderId repairOrderId);


    /**
     * 根据报修订单ID查询附件列表
     *
     * @param repairOrderId 报修订单ID
     * @return 附件列表
     */
    List<RepairAttachment> findAttachmentsByOrderId(RepairOrderId repairOrderId);

    /**
     * 删除附件
     *
     * @param attachmentId 附件ID
     * @return 是否删除成功
     */
    boolean deleteAttachment(AttachmentId attachmentId);
}
