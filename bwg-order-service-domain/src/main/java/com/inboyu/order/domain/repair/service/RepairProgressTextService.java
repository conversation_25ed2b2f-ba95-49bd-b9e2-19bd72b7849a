package com.inboyu.order.domain.repair.service;

import com.inboyu.order.domain.repair.model.RepairOrder;
import com.inboyu.order.domain.repair.model.OperatorType;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

/**
 * 报修进度文案生成服务
 * 负责根据工作流状态和业务数据生成客户端和运营端的进度文案
 */
@Service
public class RepairProgressTextService {

    /**
     * 工作流节点类型常量
     */
    public static final String NODE_TYPE_SUBMIT = "node_type.submit";
    public static final String NODE_TYPE_APPROVAL = "node_type.approval";
    public static final String NODE_TYPE_COMPLETE = "node_type.complete";
    public static final String NODE_TYPE_CONFIRM = "node_type.confirm";

    /**
     * 工作流实例状态常量
     */
    public static final String INSTANCE_STATUS_SUBMITTED = "instance_status.submitted";
    public static final String INSTANCE_STATUS_APPROVE_PASS = "instance_status.approve_pass";
    public static final String INSTANCE_STATUS_APPROVE_REJECT = "instance_status.approve_reject";
    public static final String INSTANCE_STATUS_COMPLETED = "instance_status.completed";
    public static final String INSTANCE_STATUS_CONFIRMED = "instance_status.confirmed";
    public static final String INSTANCE_STATUS_REWORK = "instance_status.rework";

    /**
     * 业务类型常量
     */
    public static final String BUSINESS_TYPE_REPAIR_REQUEST = "workflow_business_type.repair_request";

    /**
     * 生成进度文案
     *
     * @param repairOrder 报修订单
     * @param nodeType 节点类型
     * @param instanceStatus 实例状态
     * @param form 表单数据
     * @param variable 业务变量
     * @param sysRemark 系统备注
     * @param isClient 是否为客户端文案
     * @return 进度文案对象
     */
    public ProgressText generateProgressText(RepairOrder repairOrder, String nodeType, String instanceStatus, 
                                           Map<String, Object> form, Map<String, String> variable, 
                                           String sysRemark, boolean isClient) {
        
        // 根据节点类型和状态生成不同的文案
        switch (nodeType) {
            case NODE_TYPE_SUBMIT:
                return generateSubmitText(repairOrder, isClient);
            case NODE_TYPE_APPROVAL:
                return generateApprovalText(repairOrder, instanceStatus, form, variable, sysRemark, isClient);
            case NODE_TYPE_COMPLETE:
                return generateCompleteText(repairOrder, form, variable, isClient);
            case NODE_TYPE_CONFIRM:
                return generateConfirmText(repairOrder, instanceStatus, form, variable, isClient);
            default:
                return generateDefaultText(repairOrder, nodeType, instanceStatus, isClient);
        }
    }

    /**
     * 生成提交节点文案
     * 根据工单进度表格规范生成
     */
    private ProgressText generateSubmitText(RepairOrder repairOrder, boolean isClient) {
        String mainText;
        String subText;
        
        if (isClient) {
            // 客户端进度文案：主：您提交了报修，请等待门店受理
            mainText = "您提交了报修，请等待门店受理";
            // 副：{报修物品-故障标签} {：客户备注说明}
            subText = String.format("%s-%s：%s", 
                repairOrder.getRepairOrderId().getValue(),
                repairOrder.getItemTag(), 
                repairOrder.getDetail());
        } else {
            // 运营端进度文案：主：{客户/管家} {姓名} 提交了报修，请及时处理
            mainText = String.format("%s %s 提交了报修，请及时处理", 
                getSubmitterType(repairOrder), 
                repairOrder.getSubmitter());
            // 副：{报修物品-故障标签} {：客户备注说明}
            subText = String.format("%s-%s：%s", 
                repairOrder.getRepairItemId().getValue(),
                repairOrder.getItemTag(), 
                repairOrder.getDetail());
        }
        
        return new ProgressText(mainText, subText, OperatorType.tenant());
    }

    /**
     * 生成审批节点文案
     * 根据工单进度表格规范生成
     */
    private ProgressText generateApprovalText(RepairOrder repairOrder, String instanceStatus, 
                                            Map<String, Object> form, Map<String, String> variable, 
                                            String sysRemark, boolean isClient) {
        String mainText;
        String subText;
        OperatorType operatorType;
        
        switch (instanceStatus) {
            case INSTANCE_STATUS_APPROVE_PASS:
                // 受理 - 处理中
                if (isClient) {
                    // 客户端：主：{姓名} 已接单，将尽快与您联系并安排维修
                    String maintainerName = getFormValue(form, "maintainer_name", "维修人员");
                    mainText = String.format("%s 已接单，将尽快与您联系并安排维修", maintainerName);
                    subText = "-";
                } else {
                    // 运营端：主：管家 {姓名} 已接单
                    String maintainerName = getFormValue(form, "maintainer_name", "管家");
                    mainText = String.format("管家 %s 已接单", maintainerName);
                    subText = "-";
                }
                operatorType = OperatorType.maintainer();
                break;
                
            case INSTANCE_STATUS_APPROVE_REJECT:
                // 不受理 - 已完成
                if (isClient) {
                    // 客户端：主：无法为您安排维修，敬请谅解
                    mainText = "无法为您安排维修，敬请谅解";
                    // 副：{不受理标签/说明}
                    subText = getFormValue(form, "reject_reason", "不符合受理条件");
                } else {
                    // 运营端：主：管家 {姓名} 确认无法受理
                    String maintainerName = getFormValue(form, "maintainer_name", "管家");
                    mainText = String.format("管家 %s 确认无法受理", maintainerName);
                    // 副：{不受理标签/说明}
                    subText = getFormValue(form, "reject_reason", "不符合受理条件");
                }
                operatorType = OperatorType.maintainer();
                break;
                
            default:
                // 转交、挂起等其他状态
                return generateTransferOrSuspendText(repairOrder, form, variable, isClient);
        }
        
        return new ProgressText(mainText, subText, operatorType);
    }

    /**
     * 生成转交或挂起文案
     * 根据工单进度表格规范生成
     */
    private ProgressText generateTransferOrSuspendText(RepairOrder repairOrder, Map<String, Object> form, 
                                                     Map<String, String> variable, boolean isClient) {
        String mainText;
        String subText;
        OperatorType operatorType = OperatorType.maintainer();
        
        // 检查是否为转交
        if (variable != null && variable.containsKey("transfer_to")) {
            // 转交 - 处理中
            String newMaintainerName = variable.get("transfer_to");
            String oldMaintainerName = getFormValue(form, "old_maintainer_name", "原管家");
            String transferReason = getFormValue(form, "transfer_reason", "");
            
            if (isClient) {
                // 客户端：主：已转交 {姓名} 负责，将尽快为您服务
                mainText = String.format("已转交 %s 负责，将尽快为您服务", newMaintainerName);
                subText = "-";
            } else {
                // 运营端：主：已由管家 {原管家姓名} 转交给{新管家姓名} 负责
                mainText = String.format("已由管家 %s 转交给%s 负责", oldMaintainerName, newMaintainerName);
                // 副：{转交说明}
                subText = transferReason;
            }
        } else {
            // 挂起 - 处理中
            String suspendReason = getFormValue(form, "suspend_reason", "系统维护");
            String suspendEndTime = getFormValue(form, "suspend_end_time", "");
            
            if (isClient) {
                // 客户端：主：{挂起原因标签}，请耐心等待
                mainText = String.format("%s，请耐心等待", suspendReason);
                // 副：预计 {预计挂起结束时间 MM/DD} 可为您安排维修
                if (!suspendEndTime.isEmpty()) {
                    subText = String.format("预计 %s 可为您安排维修", formatDate(suspendEndTime));
                } else {
                    subText = "";
                }
            } else {
                // 运营端：主：{挂起原因标签}，工单挂起
                mainText = String.format("%s，工单挂起", suspendReason);
                // 副：预计 {预计挂起结束时间 MM/DD} 再次进行维修
                if (!suspendEndTime.isEmpty()) {
                    subText = String.format("预计 %s 再次进行维修", formatDate(suspendEndTime));
                } else {
                    subText = "";
                }
            }
        }
        
        return new ProgressText(mainText, subText, operatorType);
    }

    /**
     * 生成完成节点文案
     * 根据工单进度表格规范生成
     */
    private ProgressText generateCompleteText(RepairOrder repairOrder, Map<String, Object> form, 
                                            Map<String, String> variable, boolean isClient) {
        String mainText;
        String subText;
        String maintainerName = getFormValue(form, "maintainer_name", "维修人员");
        String completeComment = getFormValue(form, "complete_comment", "");
        
        if (isClient) {
            // 客户端：主：请您验收维修结果，如有问题可申请返工
            mainText = "请您验收维修结果，如有问题可申请返工";
            subText = "-";
        } else {
            // 运营端：主：管家 {姓名} 已完成维修
            mainText = String.format("管家 %s 已完成维修", maintainerName);
            // 副：{完成维修说明}
            subText = completeComment;
        }
        
        return new ProgressText(mainText, subText, OperatorType.maintainer());
    }

    /**
     * 生成确认节点文案
     * 根据工单进度表格规范生成
     */
    private ProgressText generateConfirmText(RepairOrder repairOrder, String instanceStatus, 
                                           Map<String, Object> form, Map<String, String> variable, 
                                           boolean isClient) {
        String mainText;
        String subText;
        OperatorType operatorType = OperatorType.tenant();
        
        switch (instanceStatus) {
            case INSTANCE_STATUS_CONFIRMED:
                // 验收 - 确认关闭 - 已关闭
                if (isClient) {
                    // 客户端：主：您确认了维修完成，报修单自动关闭
                    mainText = "您确认了维修完成，报修单自动关闭";
                    subText = "-";
                } else {
                    // 运营端：主：客户 {姓名} 确认了维修完成，报修单自动关闭
                    mainText = String.format("客户 %s 确认了维修完成，报修单自动关闭", repairOrder.getSubmitter());
                    subText = "-";
                }
                break;
                
            case INSTANCE_STATUS_REWORK:
                // 需要返工 - 处理中
                String reworkComment = getFormValue(form, "rework_comment", "");
                if (isClient) {
                    // 客户端：主：您提交了返工申请，{姓名} 将尽快为您服务
                    mainText = String.format("您提交了返工申请，%s 将尽快为您服务", 
                        getFormValue(form, "maintainer_name", "维修人员"));
                    // 副：{返工说明}
                    subText = reworkComment;
                } else {
                    // 运营端：主：客户 {姓名} 提交了返工申请，请及时处理
                    mainText = String.format("客户 %s 提交了返工申请，请及时处理", repairOrder.getSubmitter());
                    // 副：{返工说明}
                    subText = reworkComment;
                }
                break;
                
            default:
                // 定时关闭 - 已关闭
                if (isClient) {
                    // 客户端：主：已完成 48 小时，报修单自动关闭
                    mainText = "已完成 48 小时，报修单自动关闭";
                    subText = "-";
                } else {
                    // 运营端：主：已完成 48 小时，报修单自动关闭
                    mainText = "已完成 48 小时，报修单自动关闭";
                    subText = "-";
                }
                break;
        }
        
        return new ProgressText(mainText, subText, operatorType);
    }

    /**
     * 生成默认文案
     */
    private ProgressText generateDefaultText(RepairOrder repairOrder, String nodeType, String instanceStatus, boolean isClient) {
        String mainText = String.format("工单状态更新：%s", instanceStatus);
        String subText = String.format("节点类型：%s", nodeType);
        return new ProgressText(mainText, subText, OperatorType.tenant());
    }

    /**
     * 获取表单值
     */
    private String getFormValue(Map<String, Object> form, String key, String defaultValue) {
        if (form == null || !form.containsKey(key)) {
            return defaultValue;
        }
        
        Object value = form.get(key);
        if (value instanceof Map) {
            Map<String, Object> valueMap = (Map<String, Object>) value;
            return (String) valueMap.getOrDefault("value", defaultValue);
        }
        
        return value != null ? value.toString() : defaultValue;
    }

    /**
     * 格式化日期
     */
    private String formatDate(String dateStr) {
        try {
            LocalDateTime dateTime = LocalDateTime.parse(dateStr);
            return dateTime.format(DateTimeFormatter.ofPattern("MM/dd"));
        } catch (Exception e) {
            return dateStr;
        }
    }

    /**
     * 获取提交者类型
     */
    private String getSubmitterType(RepairOrder repairOrder) {
        // 根据业务逻辑判断是客户还是管家
        // 这里可以根据实际业务规则来判断
        return "客户";
    }

    /**
     * 进度文案对象
     */
    public static class ProgressText {
        private final String mainText;
        private final String subText;
        private final OperatorType operatorType;

        public ProgressText(String mainText, String subText, OperatorType operatorType) {
            this.mainText = mainText;
            this.subText = subText;
            this.operatorType = operatorType;
        }

        public String getMainText() {
            return mainText;
        }

        public String getSubText() {
            return subText;
        }

        public OperatorType getOperatorType() {
            return operatorType;
        }
    }
}
