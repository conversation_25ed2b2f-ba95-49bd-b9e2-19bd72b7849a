package com.inboyu.order.domain.repair.model;

import lombok.Builder;
import lombok.Getter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 报修订单领域对象
 * 封装报修订单的属性和业务行为
 * 按照DDD标准 生成领域对象
 */
@Getter
@Builder(toBuilder = true)
public class RepairOrder {

    /**
     * 报修订单ID
     */
    private final RepairOrderId repairOrderId;

    /**
     * 客户ID
     */
    private final CustomerId customerId;

    /**
     * 门店ID
     */
    private final StoreId storeId;

    /**
     * 房间ID
     */
    private final RoomId roomId;

    /**
     * 楼栋ID
     */
    private final BuildingId buildingId;

    /**
     * 报修物品ID
     */
    private final ItemId itemId;

    /**
     * 报修物品故障描述标签
     */
    private final String itemTag;

    /**
     * 工作流ID
     */
    private final WorkflowId workflowId;

    /**
     * 房号
     */
    private final String roomNumber;

    /**
     * 报修区域
     */
    private final String area;

    /**
     * 问题描述
     */
    private final String detail;

    /**
     * 联系电话，加密存储
     */
    private final String contactPhone;

    /**
     * 报修人姓名
     */
    private final String submitter;

    /**
     * 意向维修日期
     */
    private final LocalDate willRepairDate;

    /**
     * 意向维修开始时间段
     */
    private final LocalTime willRepairStart;

    /**
     * 意向维修结束时间段
     */
    private final LocalTime willRepairEnd;

    /**
     * 是否无人可直接入户 0-否 1-是
     */
    private final IsEnter isEnter;

    /**
     * 报修状态,字典维护
     */
    private final RepairOrderStatus status;

    /**
     * 工单类型,字典维护
     */
    private final OrderType orderType;

    /**
     * 标签状态,字典维护
     */
    private final TagStatus tagStatus;

    /**
     * 拒绝理由
     */
    private final String refuseReason;

    /**
     * 挂起原因
     */
    private final String suspendReason;

    /**
     * 预计上门时间
     */
    private final LocalDateTime preDoorTime;

    /**
     * 预计完成时间
     */
    private final LocalDateTime preRepairTime;

    /**
     * 完成时间
     */
    private final LocalDateTime finishTime;

    /**
     * 受理时间
     */
    private final LocalDateTime dealTime;

    /**
     * 责任方ID
     */
    private final ResponsibleId responsibleId;

    /**
     * 处理人ID
     */
    private final MaintainerId maintainerId;

    /**
     * 备注
     */
    private final String remark;

    /**
     * 报修订单的附件
     */
    List<RepairAttachment> attachments;

    /**
     * 受理报修订单
     *
     * @param operatorId 操作人ID
     * @param operationComment 操作说明
     * @return 受理后的报修订单
     */
    public RepairOrder accept(Long operatorId, String operationComment) {
        return this.toBuilder()
                .status(RepairOrderStatus.of(RepairOrderStatus.REPAIR_ORDER_STATUS_PROCESSING))
                .dealTime(LocalDateTime.now())
                .responsibleId(ResponsibleId.of(operatorId))
                .remark(operationComment)
                .build();
    }

    /**
     * 拒绝报修订单
     *
     * @param operatorId 操作人ID
     * @param operationComment 操作说明
     * @return 拒绝后的报修订单
     */
    public RepairOrder reject(Long operatorId, String operationComment) {
        return this.toBuilder()
                .status(RepairOrderStatus.of(RepairOrderStatus.REPAIR_ORDER_STATUS_CLOSED))
                .dealTime(LocalDateTime.now())
                .responsibleId(ResponsibleId.of(operatorId))
                .refuseReason(operationComment)
                .build();
    }

    /**
     * 完成维修
     *
     * @param maintainerId 维修人员ID
     * @param completeComment 完成说明
     * @param actualCompleteTime 实际完成时间
     * @return 完成后的报修订单
     */
    public RepairOrder complete(Long maintainerId, String completeComment, String actualCompleteTime) {
        return this.toBuilder()
                .status(RepairOrderStatus.of(RepairOrderStatus.REPAIR_ORDER_STATUS_COMPLETED))
                .maintainerId(MaintainerId.of(maintainerId))
                .finishTime(LocalDateTime.now())
                .remark(completeComment)
                .build();
    }

    /**
     * 取消报修订单
     *
     * @param cancelReason 取消原因
     * @return 取消后的报修订单
     */
    public RepairOrder cancel(String cancelReason) {
        return this.toBuilder()
                .status(RepairOrderStatus.of(RepairOrderStatus.REPAIR_ORDER_STATUS_CLOSED))
                .refuseReason(cancelReason)
                .build();
    }

    /**
     * 确认报修订单
     *
     * @param confirmType 确认类型
     * @param confirmComment 确认备注
     * @return 确认后的报修订单
     */
    public RepairOrder confirm(String confirmType, String confirmComment) {
        String newStatus = "confirm".equals(confirmType) 
            ? RepairOrderStatus.REPAIR_ORDER_STATUS_CLOSED 
            : RepairOrderStatus.REPAIR_ORDER_STATUS_PROCESSING;
        
        return this.toBuilder()
                .status(RepairOrderStatus.of(newStatus))
                .remark(confirmComment)
                .build();
    }

    /**
     * 请求返工
     *
     * @param reworkInstructions 返工说明
     * @return 返工后的报修订单
     */
    public RepairOrder requestRework(String reworkInstructions) {
        return this.toBuilder()
                .status(RepairOrderStatus.of(RepairOrderStatus.REPAIR_ORDER_STATUS_PROCESSING))
                .remark(reworkInstructions)
                .build();
    }
}