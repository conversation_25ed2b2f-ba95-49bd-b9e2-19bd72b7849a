package com.inboyu.order.domain.repair.repository;

import com.inboyu.order.domain.repair.model.RepairOrder;
import com.inboyu.order.domain.repair.model.RepairOrderId;
import com.inboyu.order.domain.repair.model.CustomerId;
import com.inboyu.order.domain.repair.model.ProjectId;
import com.inboyu.order.domain.repair.model.RoomId;
import com.inboyu.order.domain.repair.model.RepairOrderStatus;
import com.inboyu.order.domain.repair.model.OrderType;
import com.inboyu.order.domain.repair.model.TagStatus;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 报修工单仓储接口
 */
public interface RepairOrderRepository {

    /**
     * 根据报修工单ID查询
     */
    Optional<RepairOrder> findById(RepairOrderId repairOrderId);

    /**
     * 根据客户ID查询报修工单列表
     */
    List<RepairOrder> findByCustomerId(CustomerId customerId);

    /**
     * 根据项目ID查询报修工单列表
     */
    List<RepairOrder> findByProjectId(ProjectId projectId);

    /**
     * 根据房间ID查询报修工单列表
     */
    List<RepairOrder> findByRoomId(RoomId roomId);

    /**
     * 根据状态查询报修工单列表
     */
    List<RepairOrder> findByStatus(RepairOrderStatus status);

    /**
     * 根据工单类型查询报修工单列表
     */
    List<RepairOrder> findByOrderType(OrderType orderType);

    /**
     * 根据标签状态查询报修工单列表
     */
    List<RepairOrder> findByTagStatus(TagStatus tagStatus);

    /**
     * 根据处理人ID查询报修工单列表
     */
    List<RepairOrder> findByMaintainerId(Long maintainerId);

    /**
     * 根据意向维修日期查询报修工单列表
     */
    List<RepairOrder> findByWillRepairDate(LocalDate willRepairDate);

    /**
     * 根据创建时间范围查询报修工单列表
     */
    List<RepairOrder> findByCreateTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 分页查询报修工单
     */
    List<RepairOrder> findByProjectIdAndFilters(ProjectId projectId, String filterBy, String filter, 
                                               Integer pageNum, Integer pageSize);

    /**
     * 统计报修工单数量
     */
    long countByProjectId(ProjectId projectId);

    /**
     * 统计指定状态的报修工单数量
     */
    long countByProjectIdAndStatus(ProjectId projectId, RepairOrderStatus status);

    /**
     * 保存报修工单
     */
    RepairOrder save(RepairOrder repairOrder);

    /**
     * 批量保存报修工单
     */
    List<RepairOrder> saveAll(List<RepairOrder> repairOrders);

    /**
     * 更新报修工单
     */
    RepairOrder update(RepairOrder repairOrder);

    /**
     * 根据ID删除报修工单
     */
    void deleteById(RepairOrderId repairOrderId);

    /**
     * 根据项目ID删除所有报修工单
     */
    void deleteByProjectId(ProjectId projectId);

    /**
     * 更新报修工单状态
     */
    void updateStatusById(RepairOrderId repairOrderId, RepairOrderStatus status);

    /**
     * 更新报修工单标签状态
     */
    void updateTagStatusById(RepairOrderId repairOrderId, TagStatus tagStatus);

    /**
     * 更新报修工单处理人
     */
    void updateMaintainerById(RepairOrderId repairOrderId, Long maintainerId);
}
