package com.inboyu.order.domain.repair.model;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 报修订单附件ID值对象
 * 按照DDD标准，将ID封装为值对象确保类型安全
 */
@Getter
@Builder
@EqualsAndHashCode
public class AttachmentId {
    private final Long value;

    private AttachmentId(Long value) {
        if (value == null || value <= 0) {
            throw new IllegalArgumentException("报修订单附件ID不能为空或小于等于0");
        }
        this.value = value;
    }

    public static AttachmentId of(Long value) {
        return new AttachmentId(value);
    }

    public static AttachmentId of(String value) {
        return new AttachmentId(Long.valueOf(value));
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }
}
