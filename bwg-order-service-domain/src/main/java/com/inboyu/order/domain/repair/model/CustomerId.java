package com.inboyu.order.domain.repair.model;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 客户ID值对象
 * 按照DDD标准，将ID封装为值对象确保类型安全
 */
@Getter
@Builder
@EqualsAndHashCode
public class CustomerId {

    private final Long value;

    private CustomerId(Long value) {
        if (value == null || value <= 0) {
            throw new IllegalArgumentException("客户ID不能为空或小于等于0");
        }
        this.value = value;
    }

    public static CustomerId of(Long value) {
        return new CustomerId(value);
    }

    public static CustomerId of(String value) {
        return new CustomerId(Long.valueOf(value));
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }
}
