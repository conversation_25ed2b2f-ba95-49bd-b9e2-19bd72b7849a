package com.inboyu.order.domain.repair.repository;

import com.inboyu.order.domain.repair.model.RepairProgress;
import com.inboyu.order.domain.repair.model.RepairOrderId;
import com.inboyu.order.domain.repair.model.ProgressType;
import com.inboyu.order.domain.repair.model.RepairOrderStatus;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 报修进度仓储接口
 */
public interface RepairProgressRepository {

    /**
     * 根据进度ID查询
     */
    Optional<RepairProgress> findById(Long progressId);

    /**
     * 根据报修工单ID查询进度列表
     */
    List<RepairProgress> findByRepairOrderId(RepairOrderId repairOrderId);

    /**
     * 根据操作用户ID查询进度列表
     */
    List<RepairProgress> findByUserId(Long userId);

    /**
     * 根据操作者类型查询进度列表
     */
    List<RepairProgress> findByType(ProgressType type);

    /**
     * 根据处理状态查询进度列表
     */
    List<RepairProgress> findByStatus(RepairOrderStatus status);

    /**
     * 根据创建时间范围查询进度列表
     */
    List<RepairProgress> findByCreateTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据报修工单ID和操作者类型查询进度列表
     */
    List<RepairProgress> findByRepairOrderIdAndType(RepairOrderId repairOrderId, ProgressType type);

    /**
     * 根据报修工单ID查询最新的进度记录
     */
    Optional<RepairProgress> findLatestByRepairOrderId(RepairOrderId repairOrderId);

    /**
     * 根据报修工单ID查询重开记录
     */
    List<RepairProgress> findReopenByRepairOrderId(RepairOrderId repairOrderId);

    /**
     * 根据报修工单ID查询转派记录
     */
    List<RepairProgress> findTransferByRepairOrderId(RepairOrderId repairOrderId);

    /**
     * 分页查询进度记录
     */
    List<RepairProgress> findByRepairOrderIdAndFilters(RepairOrderId repairOrderId, String filterBy, String filter,
                                                      Integer pageNum, Integer pageSize);

    /**
     * 统计进度记录数量
     */
    long countByRepairOrderId(RepairOrderId repairOrderId);

    /**
     * 统计指定类型的进度记录数量
     */
    long countByRepairOrderIdAndType(RepairOrderId repairOrderId, ProgressType type);

    /**
     * 保存进度记录
     */
    RepairProgress save(RepairProgress repairProgress);

    /**
     * 批量保存进度记录
     */
    List<RepairProgress> saveAll(List<RepairProgress> repairProgresses);

    /**
     * 更新进度记录
     */
    RepairProgress update(RepairProgress repairProgress);

    /**
     * 根据ID删除进度记录
     */
    void deleteById(Long progressId);

    /**
     * 根据报修工单ID删除所有进度记录
     */
    void deleteByRepairOrderId(RepairOrderId repairOrderId);

    /**
     * 更新进度记录的备注信息
     */
    void updateCommentById(Long progressId, String comment);
}
