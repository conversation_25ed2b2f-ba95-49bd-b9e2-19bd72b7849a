package com.inboyu.order.domain.repair.repository;

import com.inboyu.order.domain.repair.model.RepairProgress;
import com.inboyu.order.domain.repair.model.RepairProgressId;
import com.inboyu.order.domain.repair.model.RepairOrderId;

import java.util.List;

/**
 * 报修进度仓储接口
 * 定义报修进度的数据访问契约
 */
public interface RepairProgressRepository {

    /**
     * 生成报修进度ID
     *
     * @return 报修进度ID
     */
    RepairProgressId generateProgressId();

    /**
     * 保存报修进度
     *
     * @param repairProgress 报修进度
     * @return 保存后的报修进度
     */
    RepairProgress save(RepairProgress repairProgress);

    /**
     * 根据订单ID查询报修进度列表（按创建时间倒序）
     *
     * @param orderId 订单ID
     * @return 进度列表
     */
    List<RepairProgress> findByRepairOrderIdOrderByCreateTimeDesc(RepairOrderId orderId);

    /**
     * 根据订单ID查询最新的一条报修进度
     *
     * @param orderId 订单ID
     * @return 最新的报修进度
     */
    RepairProgress findLatestRepairProgress(RepairOrderId orderId);
}
