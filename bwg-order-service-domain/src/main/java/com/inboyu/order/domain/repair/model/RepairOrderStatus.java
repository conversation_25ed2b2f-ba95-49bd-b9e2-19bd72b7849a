package com.inboyu.order.domain.repair.model;

import lombok.Builder;
import lombok.Getter;

/**
 * 报修工单状态
 */
@Getter
@Builder
public class RepairOrderStatus {
    /**
     * 编码
     */
    private final String code;
    /**
     * 值
     */
    private final String value;

    /** 报修工单状态枚举 */
    public final static String REPAIR_ORDER_STATUS_SUBMITTED = "repair_order_status.submitted"; // 已提单
    public final static String REPAIR_ORDER_STATUS_PROCESSING = "repair_order_status.processing";// 处理中
    public final static String REPAIR_ORDER_STATUS_COMPLETED = "repair_order_status.completed";// 已完成
    public final static String REPAIR_ORDER_STATUS_CLOSED = "repair_order_status.closed";// 已关闭

    public static RepairOrderStatus of(String code) {
        String value = getValueByCode(code);
        return RepairOrderStatus.builder().code(code).value(value).build();
    }

    private static String getValueByCode(String code) {
        switch (code) {
            case REPAIR_ORDER_STATUS_SUBMITTED:
                return "已提单";
            case REPAIR_ORDER_STATUS_PROCESSING:
                return "处理中";
            case REPAIR_ORDER_STATUS_COMPLETED:
                return "已完成";
            case REPAIR_ORDER_STATUS_CLOSED:
                return "已关闭";
            default:
                return "未知状态";
        }
    }
}
