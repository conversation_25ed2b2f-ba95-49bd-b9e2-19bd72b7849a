package com.inboyu.order.domain.repair.service.impl;

import com.inboyu.order.domain.repair.model.RepairAttachment;
import com.inboyu.order.domain.repair.model.RepairOrder;
import com.inboyu.order.domain.repair.model.RepairOrderId;
import com.inboyu.order.domain.repair.model.RepairOrderStatus;
import com.inboyu.order.domain.repair.model.WorkflowId;
import com.inboyu.order.domain.repair.repository.RepairOrderRepository;
import com.inboyu.order.domain.repair.service.RepairAttachmentDomainService;
import com.inboyu.order.domain.repair.service.RepairOrderDomainService;
import com.inboyu.order.domain.repair.service.RepairProgressDomainService;
import com.inboyu.order.domain.repair.service.RepairWorkflowDomainService;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

@Service
public class RepairOrderDomainServiceImpl implements RepairOrderDomainService {

    @Autowired
    private RepairOrderRepository repairOrderRepository;

    @Autowired
    private RepairWorkflowDomainService repairWorkflowDomainService;

    @Autowired
    private RepairProgressDomainService repairProgressDomainService;

    @Autowired
    private RepairAttachmentDomainService repairAttachmentDomainService;

    /**
     * 生成报修订单ID
     *
     * @return 报修订单ID
     */
    public RepairOrderId generateId() {
        return repairOrderRepository.generateId();
    }

    /**
     * 创建报修订单
     *
     * @param repairOrder 报修订单
     * @return 创建后的报修订单
     */
    public RepairOrder createRepairOrder(RepairOrder repairOrder) {
        // 执行业务规则验证
        validateRepairOrder(repairOrder);

        // 使用附件领域服务处理附件
        List<RepairAttachment> processedAttachments = null;
        if (repairOrder.getAttachments() != null && !repairOrder.getAttachments().isEmpty()) {
            processedAttachments = repairAttachmentDomainService.batchCreateAttachments(
                    repairOrder.getAttachments(),
                    repairOrder.getRepairOrderId()
            );
        }

        // 调用工作流服务创建报修订单工作流，获取workflow_id
/*       String workflowInstanceId = repairWorkflowDomainService.createRepairOrderWorkflow(
                repairOrder.getRepairOrderId().toString(),
                "报修订单-" + repairOrder.getItemType(),
                repairOrder.getDetail(),
                "0", // 系统用户
                "系统",
                repairOrder.getStoreId().toString()
        );*/

        //mock workflowInstanceId 数据，随机数
         String workflowInstanceId = String.valueOf(new Random().nextInt(1000000));

        // 创建包含workflowId和处理后附件的RepairOrder
        RepairOrder repairOrderWithWorkflow = RepairOrder.builder()
                .repairOrderId(repairOrder.getRepairOrderId())
                .customerId(repairOrder.getCustomerId())
                .storeId(repairOrder.getStoreId())
                .roomId(repairOrder.getRoomId())
                .buildingId(repairOrder.getBuildingId())
                .repairItemId(repairOrder.getRepairItemId())
                .itemTag(repairOrder.getItemTag())
                .workflowId(WorkflowId.of(workflowInstanceId))
                .roomNumber(repairOrder.getRoomNumber())
                .area(repairOrder.getArea())
                .detail(repairOrder.getDetail())
                .contactPhone(repairOrder.getContactPhone())
                .submitter(repairOrder.getSubmitter())
                .willRepairDate(repairOrder.getWillRepairDate())
                .willRepairStart(repairOrder.getWillRepairStart())
                .willRepairEnd(repairOrder.getWillRepairEnd())
                .isEnter(repairOrder.getIsEnter())
                .status(repairOrder.getStatus())
                .orderType(repairOrder.getOrderType())
                .tagStatus(repairOrder.getTagStatus())
                .refuseReason(repairOrder.getRefuseReason())
                .suspendReason(repairOrder.getSuspendReason())
                .preDoorTime(repairOrder.getPreDoorTime())
                .preRepairTime(repairOrder.getPreRepairTime())
                .finishTime(repairOrder.getFinishTime())
                .dealTime(repairOrder.getDealTime())
                .responsibleId(repairOrder.getResponsibleId())
                .maintainerId(repairOrder.getMaintainerId())
                .remark(repairOrder.getRemark())
                .attachments(processedAttachments)
                .build();

        // 保存报修订单
        RepairOrder savedOrder = repairOrderRepository.save(repairOrderWithWorkflow);

        // 使用进度领域服务创建提交进度
        repairProgressDomainService.createSubmitProgress(savedOrder);

        return savedOrder;
    }

    /**
     * 批量创建报修订单
     *
     * @param repairOrders 报修订单列表
     * @return 创建后的报修订单列表
     */
    public List<RepairOrder> batchCreateRepairOrders(List<RepairOrder> repairOrders) {
        // 批量验证报修订单
        repairOrders.forEach(this::validateRepairOrder);

        // 为每个订单单独处理：调用工作流服务 -> 保存订单 -> 创建进度
        return repairOrders.stream()
                .map(this::createRepairOrder) // 调用单个订单创建方法，确保每个订单都有独立的workflow_id
                .collect(Collectors.toList());
    }

    /**
     * 获取报修订单详情
     *
     * @param orderId 订单ID
     * @return 报修订单详情
     */
    public RepairOrder getRepairOrderDetail(RepairOrderId orderId) {
        return repairOrderRepository.findByRepairOrderId(orderId);
    }

    /**
     * 取消报修订单
     *
     * @param orderId 订单ID
     * @param cancelReason 取消原因
     * @return 取消后的报修订单
     */
    public RepairOrder cancelRepairOrder(RepairOrderId orderId, String cancelReason) {
        // 获取报修订单
        RepairOrder repairOrder = repairOrderRepository.findByRepairOrderId(orderId);
        if (repairOrder == null) {
            throw new IllegalArgumentException("报修订单不存在");
        }

        // 业务规则验证：只有已提交、处理中状态的订单才能被取消
        String currentStatus = repairOrder.getStatus().getValue();
        if (!RepairOrderStatus.REPAIR_ORDER_STATUS_SUBMITTED.equals(currentStatus) &&
            !RepairOrderStatus.REPAIR_ORDER_STATUS_PROCESSING.equals(currentStatus)) {
            throw new IllegalStateException("当前状态的报修订单不能取消，当前状态：" + currentStatus);
        }

        // 更新订单状态为已取消
        RepairOrder cancelledOrder = repairOrder.cancel(cancelReason);

        // 保存更新后的订单
        RepairOrder savedOrder = repairOrderRepository.save(cancelledOrder);

        // 创建取消进度记录
        repairProgressDomainService.createCancelProgress(savedOrder, cancelReason);

        return savedOrder;
    }


    /**
     * 确认报修订单
     *
     * @param orderId 订单ID
     * @param confirmType 确认类型（confirm/rework）
     * @param confirmComment 确认备注
     * @param reworkInstructions 返工说明
     * @return 确认后的报修订单
     */
    public RepairOrder confirmRepairOrder(RepairOrderId orderId, String confirmType, String confirmComment, String reworkInstructions) {
        // 获取报修订单
        RepairOrder repairOrder = repairOrderRepository.findByRepairOrderId(orderId);
        if (repairOrder == null) {
            throw new IllegalArgumentException("报修订单不存在");
        }

        // 业务规则验证：只有已完成状态的订单才能被确认
        if (!RepairOrderStatus.REPAIR_ORDER_STATUS_COMPLETED.equals(repairOrder.getStatus().getValue())) {
            throw new IllegalStateException("只有已完成状态的报修订单才能被确认");
        }

        // 根据确认类型更新订单状态
        RepairOrder updatedOrder;
        if ("confirm".equals(confirmType)) {
            // 确认完成
            updatedOrder = repairOrder.confirm(confirmType, confirmComment);
        } else if ("rework".equals(confirmType)) {
            // 请求返工
            if (reworkInstructions == null || reworkInstructions.trim().isEmpty()) {
                throw new IllegalArgumentException("返工说明不能为空");
            }
            updatedOrder = repairOrder.requestRework(reworkInstructions);
        } else {
            throw new IllegalArgumentException("不支持的确认类型：" + confirmType);
        }

        // 保存更新后的订单
        RepairOrder savedOrder = repairOrderRepository.save(updatedOrder);

        // 创建确认进度记录
        repairProgressDomainService.createConfirmProgress(savedOrder, confirmType, confirmComment, reworkInstructions);

        return savedOrder;
    }

    @Override
    public RepairOrder acceptRepairOrder(RepairOrderId orderId, Long operatorId, String operationComment) {
        System.out.println("受理报修订单，订单ID：" + orderId.getValue() + "，操作人ID：" + operatorId);
        
        // 获取报修订单
        RepairOrder repairOrder = repairOrderRepository.findByRepairOrderId(orderId);
        if (repairOrder == null) {
            throw new IllegalArgumentException("报修订单不存在");
        }
        
        // 业务规则验证：只有已提交状态的订单才能被受理
        if (!RepairOrderStatus.REPAIR_ORDER_STATUS_SUBMITTED.equals(repairOrder.getStatus().getValue())) {
            throw new IllegalStateException("只有已提交状态的报修订单才能被受理");
        }
        
        // 更新订单状态为已受理
        repairOrder.accept(operatorId, operationComment);
        
        // 保存到数据库
        RepairOrder savedOrder = repairOrderRepository.save(repairOrder);
        
        System.out.println("受理报修订单成功，订单ID：" + orderId.getValue());
        return savedOrder;
    }

    @Override
    public RepairOrder rejectRepairOrder(RepairOrderId orderId, Long operatorId, String operationComment) {
        System.out.println("不受理报修订单，订单ID：" + orderId.getValue() + "，操作人ID：" + operatorId);
        
        // 获取报修订单
        RepairOrder repairOrder = repairOrderRepository.findByRepairOrderId(orderId);
        if (repairOrder == null) {
            throw new IllegalArgumentException("报修订单不存在");
        }
        
        // 业务规则验证：只有已提交状态的订单才能被不受理
        if (!RepairOrderStatus.REPAIR_ORDER_STATUS_SUBMITTED.equals(repairOrder.getStatus().getValue())) {
            throw new IllegalStateException("只有已提交状态的报修订单才能被不受理");
        }
        
        // 更新订单状态为已拒绝
        repairOrder.reject(operatorId, operationComment);
        
        // 保存到数据库
        RepairOrder savedOrder = repairOrderRepository.save(repairOrder);
        
        System.out.println("不受理报修订单成功，订单ID：" + orderId.getValue());
        return savedOrder;
    }

    @Override
    public RepairOrder completeRepairOrder(RepairOrderId orderId, Long maintainerId, String completeComment, String actualCompleteTime) {
        System.out.println("完成维修，订单ID：" + orderId.getValue() + "，维修人员ID：" + maintainerId);
        
        // 获取报修订单
        RepairOrder repairOrder = repairOrderRepository.findByRepairOrderId(orderId);
        if (repairOrder == null) {
            throw new IllegalArgumentException("报修订单不存在");
        }
        
        // 业务规则验证：只有处理中状态的订单才能被完成
        if (!RepairOrderStatus.REPAIR_ORDER_STATUS_PROCESSING.equals(repairOrder.getStatus().getValue())) {
            throw new IllegalStateException("只有处理中状态的报修订单才能被完成");
        }
        
        // 更新订单状态为已完成
        repairOrder.complete(maintainerId, completeComment, actualCompleteTime);
        
        // 保存到数据库
        RepairOrder savedOrder = repairOrderRepository.save(repairOrder);
        
        System.out.println("完成维修成功，订单ID：" + orderId.getValue());
        return savedOrder;
    }

    /**
     * 验证报修订单业务规则
     *
     * @param repairOrder 报修订单
     */
    private void validateRepairOrder(RepairOrder repairOrder) {
        if (repairOrder == null) {
            throw new IllegalArgumentException("报修订单不能为空");
        }
        if (repairOrder.getCustomerId() == null) {
            throw new IllegalArgumentException("客户ID不能为空");
        }
        if (repairOrder.getStoreId() == null) {
            throw new IllegalArgumentException("门店ID不能为空");
        }
        if (repairOrder.getRoomId() == null) {
            throw new IllegalArgumentException("房间ID不能为空");
        }
        // 可以添加更多业务规则验证
    }

    /**
     * 运营端-报修订单分页列表
     *
     * @param pageNum      页码
     * @param pageSize     每页大小
     * @param customerId   客户ID
     * @param storeId      门店ID
     * @param status       订单状态
     * @param responsibleId 负责人ID
     * @param roomNum      房间号
     * @param roomId       房间ID
     * @return 分页结果
     */
    @Override
    public Pagination<RepairOrder> pageRepairOrder(
            Integer pageNum, Integer pageSize,
            String customerId, String storeId, String status,
            String responsibleId, String roomNum, String roomId) {

        // 参数验证
        if (pageNum == null || pageNum < 1) {
            throw new IllegalArgumentException("页码必须大于0");
        }
        if (pageSize == null || pageSize < 1 || pageSize > 100) {
            throw new IllegalArgumentException("每页大小必须在1-100之间");
        }

        // 转换参数类型
        Long customerIdLong = customerId != null && !customerId.trim().isEmpty() ? Long.valueOf(customerId) : null;
        Long storeIdLong = storeId != null && !storeId.trim().isEmpty() ? Long.valueOf(storeId) : null;
        Long responsibleIdLong = responsibleId != null && !responsibleId.trim().isEmpty() ? Long.valueOf(responsibleId) : null;
        Long roomIdLong = roomId != null && !roomId.trim().isEmpty() ? Long.valueOf(roomId) : null;

        // 处理状态参数
        List<String> statusList = null;
        if (status != null && !status.trim().isEmpty()) {
            statusList = List.of(status.split(","));
        }

        // 调用仓储层分页查询
        return repairOrderRepository.pageRepairOrder(
                pageNum, pageSize, customerIdLong, storeIdLong, statusList,
                responsibleIdLong, roomIdLong, roomNum);
    }

}
