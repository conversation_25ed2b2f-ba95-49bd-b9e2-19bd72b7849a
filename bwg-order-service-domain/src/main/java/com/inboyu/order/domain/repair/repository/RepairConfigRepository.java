package com.inboyu.order.domain.repair.repository;

import com.inboyu.order.domain.repair.model.RepairConfig;
import com.inboyu.order.domain.repair.model.RepairConfigId;
import com.inboyu.order.domain.repair.model.RepairItemId;
import com.inboyu.order.domain.repair.model.StoreId;

import java.util.List;

/**
 * 报修配置仓储接口
 * 定义报修配置的数据访问契约
 */
public interface RepairConfigRepository {

    /**
     * 生成报修配置ID
     *
     * @return 报修配置ID
     */
    RepairConfigId generateId();

    /**
     * 保存报修配置
     *
     * @param repairConfig 报修配置
     * @return 保存后的报修配置
     */
    RepairConfig save(RepairConfig repairConfig);

    /**
     * 更新报修配置
     *
     * @param repairConfig 报修配置
     * @return 更新后的报修配置
     */
    RepairConfig update(RepairConfig repairConfig);

    /**
     * 根据配置ID查询报修配置
     *
     * @param configId 配置ID
     * @return 报修配置，不存在返回null
     */
    RepairConfig findById(RepairConfigId configId);

    /**
     * 根据门店ID查询报修配置
     *
     * @param storeId 门店ID
     * @return 报修配置，不存在返回null
     */
    RepairConfig findByStoreId(StoreId storeId);

    /**
     * 保存门店可报修物品配置
     *
     * @param configId    配置ID
     * @param storeId     门店ID
     * @param repairItemIds 可报修物品ID列表
     */
    void saveStoreRepairItems(RepairConfigId configId, StoreId storeId, List<RepairItemId> repairItemIds);

    /**
     * 更新门店可报修物品配置（逻辑删除旧的，新增新的）
     *
     * @param configId    配置ID
     * @param storeId     门店ID
     * @param repairItemIds 可报修物品ID列表
     */
    void updateStoreRepairItems(RepairConfigId configId, StoreId storeId, List<RepairItemId> repairItemIds);

    /**
     * 根据配置ID查询门店可报修物品ID列表
     *
     * @param storeId 配置ID
     * @return 可报修物品ID列表
     */
    List<RepairItemId> findRepairItemIdsByStoreId(StoreId storeId);
}
