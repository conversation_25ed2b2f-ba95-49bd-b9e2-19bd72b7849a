package com.inboyu.order.domain.repair.model;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 维修人员ID值对象
 * 按照DDD标准，将ID封装为值对象确保类型安全
 * `maintainer_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '处理人ID',
 */
@Getter
@Builder
@EqualsAndHashCode
public class MaintainerId {

    private final Long value;

    private MaintainerId(Long value) {
        if (value == null || value < 0) {
            throw new IllegalArgumentException("维修人员ID不能为空或小于0");
        }
        this.value = value;
    }

    public static MaintainerId of(Long value) {
        return new MaintainerId(value);
    }

    public static MaintainerId of(String value) {
        return new MaintainerId(Long.valueOf(value));
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }
}
