package com.inboyu.order.domain.repair.model;

import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 当天报修开放状态值对象
 * 封装是否开放当天预约的状态
 */
@Getter
@EqualsAndHashCode
public class RepairTodayEnable {

    private final String code;
    private final String title;

    private RepairTodayEnable(String code, String title) {
        if (code == null || code.trim().isEmpty()) {
            throw new IllegalArgumentException("当天报修开放状态编码不能为空");
        }
        if (title == null || title.trim().isEmpty()) {
            throw new IllegalArgumentException("当天报修开放状态标题不能为空");
        }
        this.code = code;
        this.title = title;
    }

    public static RepairTodayEnable create(String code, String title) {
        return new RepairTodayEnable(code, title);
    }

    // 预定义的常量
    public static final RepairTodayEnable DISABLED = new RepairTodayEnable("status.disabled", "停用");
    public static final RepairTodayEnable ENABLED = new RepairTodayEnable("status.enabled", "启用");

    @Override
    public String toString() {
        return title;
    }
}
