package com.inboyu.order.domain.repair.service;

import com.inboyu.order.domain.repair.model.RepairOrder;
import com.inboyu.order.domain.repair.model.RepairOrderId;
import com.inboyu.order.domain.repair.model.RepairProgress;
import com.inboyu.order.domain.repair.model.RepairProgressId;

import java.util.Map;

/**
 * 工作流事件领域服务接口
 * 负责处理工作流相关的业务逻辑，包括工作流创建、审批、完成等事件的处理
 */
public interface WorkflowEventDomainService {

    /**
     * 处理工作流创建事件
     * 当报修工单的工作流被创建时，生成初始的提交进度记录
     *
     * @param businessId 业务ID
     * @param businessType 业务类型
     */
    void handleWorkflowCreateEvent(String businessId, String businessType);

    /**
     * 处理工作流审批事件
     * 当工作流审批节点被处理时（受理、不受理、转交、挂起等），更新订单状态并生成进度记录
     *
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @param operateType 操作类型
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @param comment 操作备注
     * @param instanceStatus 实例状态
     * @param variables 业务变量
     */
    void handleWorkflowApprovalEvent(String businessId, String businessType, String operateType, 
                                   Long operatorId, String operatorName, String comment, 
                                   String instanceStatus, Map<String, String> variables);

    /**
     * 处理工作流完成事件
     * 当工作流完成时，更新订单状态并生成完成进度记录
     *
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     */
    void handleWorkflowCompleteEvent(String businessId, String businessType, Long operatorId, String operatorName);

    /**
     * 检查是否为报修业务类型
     *
     * @param businessType 业务类型
     * @return 是否为报修业务类型
     */
    boolean isRepairBusinessType(String businessType);

    /**
     * 根据业务ID获取报修订单
     *
     * @param businessId 业务ID
     * @return 报修订单
     */
    RepairOrder getRepairOrderByBusinessId(String businessId);

    /**
     * 根据审批结果更新报修订单状态
     *
     * @param repairOrder 报修订单
     * @param operateType 操作类型
     * @param operatorId 操作人ID
     * @param comment 操作备注
     * @return 更新后的报修订单
     */
    RepairOrder updateRepairOrderByApproval(RepairOrder repairOrder, String operateType, Long operatorId, String comment);

    /**
     * 生成工作流进度记录
     *
     * @param repairOrder 报修订单
     * @param nodeType 节点类型
     * @param instanceStatus 实例状态
     * @param form 表单数据
     * @param variable 业务变量
     * @param sysRemark 系统备注
     * @param operatorId 操作人ID
     * @param operatorName 操作人姓名
     * @return 生成的进度记录
     */
    RepairProgress generateWorkflowProgress(RepairOrder repairOrder, String nodeType, String instanceStatus,
                                          java.util.Map<String, Object> form, java.util.Map<String, String> variable,
                                          String sysRemark, Long operatorId, String operatorName);
}
