package com.inboyu.order.domain.repair.model;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 进度业务ID值对象
 * 按照DDD标准，将ID封装为值对象确保类型安全
 */
@Getter
@Builder
@EqualsAndHashCode
public class ProgressId {

    private final Long value;

    private ProgressId(Long value) {
        if (value == null || value <= 0) {
            throw new IllegalArgumentException("进度业务ID不能为空或小于等于0");
        }
        this.value = value;
    }

    public static ProgressId of(Long value) {
        return new ProgressId(value);
    }

    public static ProgressId of(String value) {
        return new ProgressId(Long.valueOf(value));
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }
}