package com.inboyu.order.domain.repair.model;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 楼栋ID值对象
 * 按照DDD标准，将ID封装为值对象确保类型安全
 */
@Getter
@Builder
@EqualsAndHashCode
public class BuildingId {

    private final Long value;

    private BuildingId(Long value) {
        if (value == null || value <= 0) {
            throw new IllegalArgumentException("楼栋ID不能为空或小于等于0");
        }
        this.value = value;
    }

    public static BuildingId of(Long value) {
        return new BuildingId(value);
    }

    public static BuildingId of(String value) {
        return new BuildingId(Long.valueOf(value));
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }
}
