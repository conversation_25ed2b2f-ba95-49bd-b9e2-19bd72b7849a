package com.inboyu.order.domain.repair.service;

import com.inboyu.order.domain.repair.model.RepairConfig;
import com.inboyu.order.domain.repair.model.RepairConfigId;
import com.inboyu.order.domain.repair.model.RepairItem;
import com.inboyu.order.domain.repair.model.StoreId;

import java.util.List;

/**
 * 报修配置领域服务接口
 * 定义报修配置相关的业务逻辑
 */
public interface RepairConfigDomainService {

    /**
     * 生成报修配置ID
     *
     * @return 报修配置ID
     */
    RepairConfigId generateConfigId();

    /**
     * 创建门店报修配置
     *
     * @param repairConfig 报修配置
     * @return 创建后的报修配置
     */
    RepairConfig createRepairConfig(RepairConfig repairConfig);

    /**
     * 更新门店报修配置
     *
     * @param repairConfig 报修配置
     * @return 更新后的报修配置
     */
    RepairConfig updateRepairConfig(RepairConfig repairConfig);

    /**
     * 根据配置ID查询报修配置详情（包含可报修物品）
     *
     * @param storeId 配置ID
     * @return 报修配置详情
     */
    RepairConfig getRepairConfigDetail(StoreId storeId);

    /**
     * 获取系统所有报修物品列表
     *
     * @return 报修物品列表
     */
    List<RepairItem> getAllRepairItems();

    /**
     * 验证门店是否已存在报修配置
     *
     * @param storeId 门店ID
     * @return true-存在，false-不存在
     */
    boolean existsByStoreId(StoreId storeId);
}
