package com.inboyu.order.domain.repair.service.impl;

import com.inboyu.order.domain.repair.model.RepairConfig;
import com.inboyu.order.domain.repair.model.RepairConfigId;
import com.inboyu.order.domain.repair.model.RepairItem;
import com.inboyu.order.domain.repair.model.StoreId;
import com.inboyu.order.domain.repair.repository.RepairConfigRepository;
import com.inboyu.order.domain.repair.repository.RepairItemRepository;
import com.inboyu.order.domain.repair.service.RepairConfigDomainService;
import com.inboyu.order.exception.ResponseCode;
import com.inboyu.spring.cloud.starter.common.exception.AppException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 报修配置领域服务实现
 * 实现报修配置相关的业务逻辑
 */
@Service
public class RepairConfigDomainServiceImpl implements RepairConfigDomainService {

    @Autowired
    private RepairConfigRepository repairConfigRepository;

    @Autowired
    private RepairItemRepository repairItemRepository;

    @Override
    public RepairConfigId generateConfigId() {
        return repairConfigRepository.generateId();
    }

    @Override
    public RepairConfig createRepairConfig(RepairConfig repairConfig) {
        // 验证时间配置是否合理
        if (!repairConfig.isTimeConfigValid()) {
            throw new AppException(ResponseCode.REPAIR_CONFIG_TIME_INVALID);
        }

        // 保存报修配置
        RepairConfig savedConfig = repairConfigRepository.save(repairConfig);

        // 保存门店可报修物品配置
        if (repairConfig.getRepairItemIds() != null && !repairConfig.getRepairItemIds().isEmpty()) {
            repairConfigRepository.saveStoreRepairItems(
                    savedConfig.getRepairConfigId(),
                    savedConfig.getStoreId(),
                    repairConfig.getRepairItemIds()
            );
        }

        return savedConfig;
    }

    @Override
    public RepairConfig updateRepairConfig(RepairConfig repairConfig) {
        // 验证时间配置是否合理
        if (!repairConfig.isTimeConfigValid()) {
            throw new AppException(ResponseCode.REPAIR_CONFIG_TIME_INVALID);
        }

        // 更新报修配置
        RepairConfig updatedConfig = repairConfigRepository.update(repairConfig);

        // 更新门店可报修物品配置
        if (repairConfig.getRepairItemIds() != null) {
            repairConfigRepository.updateStoreRepairItems(
                    updatedConfig.getRepairConfigId(),
                    updatedConfig.getStoreId(),
                    repairConfig.getRepairItemIds()
            );
        }

        return repairConfig;
    }

    @Override
    public RepairConfig getRepairConfigDetail(StoreId storeId) {
        RepairConfig repairConfig = repairConfigRepository.findByStoreId(storeId);
        if (repairConfig == null) {
            return null;
        }

        // 查询可报修物品ID列表
        List<com.inboyu.order.domain.repair.model.RepairItemId> repairItemIds = 
                repairConfigRepository.findRepairItemIdsByStoreId(storeId);

        // 重新构建包含可报修物品的配置对象
        return RepairConfig.builder()
                .repairConfigId(repairConfig.getRepairConfigId())
                .storeId(repairConfig.getStoreId())
                .repairStartTime(repairConfig.getRepairStartTime())
                .repairEndTime(repairConfig.getRepairEndTime())
                .repairTimeInterval(repairConfig.getRepairTimeInterval())
                .repairWeekDay(repairConfig.getRepairWeekDay())
                .maxRepairDays(repairConfig.getMaxRepairDays())
                .repairTodayEnable(repairConfig.getRepairTodayEnable())
                .repairItemIds(repairItemIds)
                .build();
    }

    @Override
    public List<RepairItem> getAllRepairItems() {
        return repairItemRepository.findAllActiveItems();
    }

    @Override
    public boolean existsByStoreId(StoreId storeId) {
        RepairConfig config = repairConfigRepository.findByStoreId(storeId);
        return config != null;
    }
}
