package com.inboyu.order.domain.repair.service;

/**
 * 工作流服务接口
 * 定义工作流相关的操作，由Infrastructure层实现
 */
public interface WorkflowService {

    /**
     * 创建报修工单工作流
     *
     * @param repairOrderId 报修工单ID
     * @param title 标题
     * @param description 描述
     * @param userId 用户ID
     * @param userName 用户名
     * @param storeId 门店ID
     * @return 工作流实例ID
     */
    String createRepairOrderWorkflow(String repairOrderId, String title, String description, 
                                   String userId, String userName, String storeId);

    /**
     * 取消工作流
     *
     * @param instanceId 工作流实例ID
     * @param userName 用户名
     * @param userType 用户类型
     * @param userId 用户ID
     * @param comment 备注
     * @return 取消结果
     */
    String cancelWorkflow(String instanceId, String userName, String userType, String userId, String comment);

    /**
     * 工作流审批
     *
     * @param instanceStepId 实例步骤ID
     * @param operateType 操作类型
     * @param comment 备注
     * @param userType 用户类型
     * @param userId 用户ID
     * @param userName 用户名
     * @return 审批结果
     */
    String workflowApproval(String instanceStepId, String operateType, String comment,
                          String userType, String userId, String userName);

    /**
     * 查询工作流实例详情
     *
     * @param workflowInstanceId 工作流实例ID
     * @return 工作流实例详情JSON字符串
     */
    String getWorkflowInstance(String workflowInstanceId);
}
