package com.inboyu.order.domain.repair.repository;

import com.inboyu.order.domain.repair.model.RepairItem;
import com.inboyu.order.domain.repair.model.RepairItemId;

import java.util.List;

/**
 * 报修物品仓储接口
 * 定义报修物品的数据访问契约
 */
public interface RepairItemRepository {

    /**
     * 获取所有启用的报修物品列表（无分页）
     *
     * @return 报修物品列表
     */
    List<RepairItem> findAllActiveItems();

    /**
     * 根据物品ID列表查询报修物品
     *
     * @param itemIds 物品ID列表
     * @return 报修物品列表
     */
    List<RepairItem> findByIds(List<RepairItemId> itemIds);

    /**
     * 根据物品ID查询报修物品
     *
     * @param itemId 物品ID
     * @return 报修物品，不存在返回null
     */
    RepairItem findById(RepairItemId itemId);

    /**
     * 根据分类查询报修物品
     *
     * @param kind 物品分类
     * @return 报修物品列表
     */
    List<RepairItem> findByKind(String kind);
}
