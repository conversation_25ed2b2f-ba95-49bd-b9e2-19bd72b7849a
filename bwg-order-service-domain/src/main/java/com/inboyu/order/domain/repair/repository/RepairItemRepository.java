package com.inboyu.order.domain.repair.repository;

import com.inboyu.order.domain.repair.model.RepairItem;
import com.inboyu.order.domain.repair.model.RepairItemId;
import com.inboyu.order.domain.repair.model.StoreId;

import java.util.List;

/**
 * 报修物品仓储接口
 * 定义报修物品的数据访问契约
 */
public interface RepairItemRepository {

    /**
     * 获取所有启用的报修物品列表（无分页）
     *
     * @return 报修物品列表
     */
    List<RepairItem> findAllActiveItems();

    /**
     * 根据物品ID列表查询报修物品
     *
     * @param itemIds 物品ID列表
     * @return 报修物品列表
     */
    List<RepairItem> findByIds(List<RepairItemId> itemIds);

    /**
     * 根据门店ID和物品名称模糊搜索门店可报修物品
     *
     * @param storeId 门店ID
     * @param keyword 搜索关键词
     * @return 报修物品列表
     */
    List<RepairItem> findStoreRepairItemsByNameContaining(StoreId storeId, String keyword);
}
