package com.inboyu.order.domain.repair.model;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * 报修订单附件聚合根
 */

@Getter
@Builder
public class RepairAttachment
{
    /**
     * 附件ID
     */
    private final AttachmentId attachmentId;

    /**
     * 文件名
     */
    private final String fileName;

    /**
     * 文件URL
     */
    private final String fileUrl;

    /**
     * 文件类型
     */
    private final String fileType;

    /**
     * 附件类型
     */
    private final String attachmentType;

    /**
     * 文件大小(字节)
     */
    private final Long fileSize;

    /**
     * 上传时间
     */
    private final LocalDateTime uploadTime;
}
