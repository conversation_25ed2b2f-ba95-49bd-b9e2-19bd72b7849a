package com.inboyu.order.app.dto.response.repair;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * 报修订单详情响应DTO
 * 根据以上生成
 */
@Data
@Schema(description = "报修订单详情响应")
public class RepairOrderDetailResponseDTO {

    @Schema(description = "报修订单ID", example = "200001")
    private String repairOrderId;

    @Schema(description = "客户ID", example = "1001")
    private String customerId;

    @Schema(description = "门店ID", example = "2001")
    private String storeId;

    @Schema(description = "房间ID", example = "3001")
    private String roomId;

    @Schema(description = "楼栋ID", example = "4001")
    private String buildingId;

    @Schema(description = "报修物品ID", example = "5001")
    private String itemId;

    @Schema(description = "报修物品故障描述标签", example = "水龙头漏水")
    private String itemTag;

    @Schema(description = "工作流ID", example = "6001")
    private String workflowId;

    @Schema(description = "房号", example = "A-1001")
    private String roomNumber;

    @Schema(description = "报修区域", example = "卫生间")
    private String area;

    @Schema(description = "问题描述", example = "卫生间水龙头持续滴水，影响正常使用")
    private String detail;

    @Schema(description = "联系电话", example = "13800138000")
    private String contactPhone;

    @Schema(description = "报修人姓名", example = "张三")
    private String submitter;

    @Schema(description = "意向维修日期", example = "2025-09-20")
    private LocalDate willRepairDate;

    @Schema(description = "意向维修开始时间段", example = "09:00:00")
    private LocalTime willRepairStart;

    @Schema(description = "意向维修结束时间段", example = "18:00:00")
    private LocalTime willRepairEnd;

    @Schema(description = "是否无人可直接入户 0-否 1-是", example = "0")
    private Integer isEnter;

    @Schema(description = "报修状态,字典维护", example = "repair_order_status.submitted")
    private String status;

    @Schema(description = "工单类型,字典维护", example = "repair_order_type.resident")
    private String orderType;

    @Schema(description = "标签状态,字典维护", example = "repair_tag_status.normal")
    private String tagStatus;

    @Schema(description = "拒绝理由", example = "业主不在家")
    private String refuseReason;

    @Schema(description = "挂起原因", example = "等待配件")
    private String suspendReason;

    @Schema(description = "预计上门时间", example = "2025-09-21 10:00:00")
    private String preDoorTime;

    @Schema(description = "预计完成时间", example = "2025-09-21 15:00:00")
    private String preRepairTime;

    @Schema(description = "完成时间", example = "2025-09-21 14:30:00")
    private String finishTime;

    @Schema(description = "受理时间", example = "2025-09-20 11:00:00")
    private String dealTime;

    @Schema(description = "责任方ID", example = "7001")
    private String responsibleId;

    @Schema(description = "处理人ID", example = "8001")
    private String maintainerId;

    @Schema(description = "备注", example = "请在工作时间联系")
    private String remark;

    @Schema(description = "创建时间", example = "2025-09-17 10:30:00")
    private String createTime;

    @Schema(description = "报修附件列表")
    private List<RepairAttachmentResponseDTO> repairAttachments;

    @Schema(description = "报修物品信息")
    private RepairItemResponseDTO repairItem;

    @Schema(description = "最新的一条报修进度信息")
    private RepairProgressResponseDTO latestRepairProgress;
}

