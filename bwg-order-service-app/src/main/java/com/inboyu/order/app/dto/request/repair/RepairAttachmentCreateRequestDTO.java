package com.inboyu.order.app.dto.request.repair;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

/**
 * 新增报修附件请求DTO
 */
@Data
@Schema(description = "新增报修附件请求")
public class RepairAttachmentCreateRequestDTO {

    @Schema(description = "文件名", example = "repair_image.jpg")
    @NotBlank(message = "文件名不能为空")
    private String fileName;

    @Schema(description = "文件URL", example = "https://example.com/repair_image.jpg")
    @NotBlank(message = "文件URL不能为空")
    private String fileUrl;

    @Schema(description = "文件类型", example = "repair_file_type.image")
    @NotBlank(message = "文件类型不能为空")
    private String fileType;

    @Schema(description = "附件类型", example = "repair_attachment_type.repair_image")
    @NotBlank(message = "附件类型不能为空")
    private String attachmentType;

    @Schema(description = "文件大小(字节)", example = "1024000")
    @NotNull(message = "文件大小不能为空")
    @Positive(message = "文件大小必须大于0")
    private Long fileSize;
}
