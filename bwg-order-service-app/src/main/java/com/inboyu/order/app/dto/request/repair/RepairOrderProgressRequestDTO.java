package com.inboyu.order.app.dto.request.repair;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 客户端获取报修进度请求DTO
 */
@Data
@Schema(description = "客户端获取报修进度请求")
public class RepairOrderProgressRequestDTO {

    @Schema(description = "报修工单ID", example = "123456789")
    @NotNull(message = "报修工单ID不能为空")
    private Long repairOrderId;
}
