package com.inboyu.order.app.application.impl;

import com.inboyu.order.app.application.RepairConfigAppService;
import com.inboyu.order.app.dto.converter.RepairConfigAppConverter;
import com.inboyu.order.app.dto.request.repair.RepairConfigCreateRequestDTO;
import com.inboyu.order.app.dto.request.repair.RepairConfigUpdateRequestDTO;
import com.inboyu.order.app.dto.response.repair.RepairConfigDetailResponseDTO;
import com.inboyu.order.app.dto.response.repair.RepairItemResponseDTO;
import com.inboyu.order.domain.repair.model.*;
import com.inboyu.order.domain.repair.repository.RepairItemRepository;
import com.inboyu.order.domain.repair.service.RepairConfigDomainService;
import com.inboyu.order.exception.ResponseCode;
import com.inboyu.spring.cloud.starter.common.exception.AppException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 报修配置应用服务实现
 * 实现报修配置相关的业务用例
 */
@Service
public class RepairConfigAppServiceImpl implements RepairConfigAppService {

    @Autowired
    private RepairConfigDomainService repairConfigDomainService;

    @Autowired
    private RepairItemRepository repairItemRepository;

    @Autowired
    private RepairConfigAppConverter repairConfigAppConverter;

    @Override
    public List<RepairItemResponseDTO> getAllRepairItems() {
        List<RepairItem> repairItems = repairConfigDomainService.getAllRepairItems();
        return repairConfigAppConverter.toRepairItemResponseDTOs(repairItems);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RepairConfigDetailResponseDTO createRepairConfig(RepairConfigCreateRequestDTO dto) {
        // 验证门店是否已存在报修配置
        StoreId storeId = StoreId.of(Long.valueOf(dto.getStoreId()));
        if (repairConfigDomainService.existsByStoreId(storeId)) {
            throw new AppException(ResponseCode.REPAIR_CONFIG_ALREADY_EXIST);
        }

        // 生成配置ID
        RepairConfigId configId = repairConfigDomainService.generateConfigId();

        // 转换为领域对象
        RepairConfig repairConfig = repairConfigAppConverter.toRepairConfig(configId, dto);

        // 创建报修配置
        RepairConfig createdConfig = repairConfigDomainService.createRepairConfig(repairConfig);

        // 查询报修物品详情
        List<RepairItem> repairItems = List.of();
        if (createdConfig.getRepairItemIds() != null && !createdConfig.getRepairItemIds().isEmpty()) {
            repairItems = repairItemRepository.findByIds(createdConfig.getRepairItemIds());
        }

        return repairConfigAppConverter.toDetailResponseDTO(createdConfig, repairItems);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RepairConfigDetailResponseDTO updateRepairConfig(RepairConfigUpdateRequestDTO dto) {
        // 验证配置是否存在
        StoreId repairConfigStoreId = StoreId.of(Long.valueOf(dto.getStoreId()));
        RepairConfig existingConfig = repairConfigDomainService.getRepairConfigDetail(repairConfigStoreId);
        if (existingConfig == null) {
            throw new AppException(ResponseCode.REPAIR_CONFIG_NOT_EXIST);
        }

        // 转换为领域对象
        RepairConfig repairConfig = repairConfigAppConverter.toRepairConfig(dto);

        // 更新报修配置
        RepairConfig updatedConfig = repairConfigDomainService.updateRepairConfig(repairConfig);

        // 查询报修物品详情
        List<RepairItem> repairItems = List.of();
        if (updatedConfig.getRepairItemIds() != null && !updatedConfig.getRepairItemIds().isEmpty()) {
            repairItems = repairItemRepository.findByIds(updatedConfig.getRepairItemIds());
        }

        return repairConfigAppConverter.toDetailResponseDTO(updatedConfig, repairItems);
    }

    @Override
    public RepairConfigDetailResponseDTO getRepairConfigDetail(String storeId) {
        // 查询报修配置
        StoreId repairConfigStoreId = StoreId.of(Long.valueOf(storeId));
        RepairConfig repairConfig = repairConfigDomainService.getRepairConfigDetail(repairConfigStoreId);
        if (repairConfig == null) {
            throw new AppException(ResponseCode.REPAIR_CONFIG_NOT_EXIST);
        }

        // 查询可报修物品详情
        List<RepairItem> repairItems = List.of();
        if (repairConfig.getRepairItemIds() != null && !repairConfig.getRepairItemIds().isEmpty()) {
            // 直接通过RepairItemRepository查询物品详情
            repairItems = repairItemRepository.findByIds(repairConfig.getRepairItemIds());
        }

        // 转换为响应DTO
        return repairConfigAppConverter.toDetailResponseDTO(repairConfig, repairItems);
    }
}
