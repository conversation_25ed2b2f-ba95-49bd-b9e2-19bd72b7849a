package com.inboyu.order.app.dto.request.repair;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 取消工作流请求DTO
 */
@Data
public class CancelWorkflowRequestDTO {

    @Schema(description = "撤销说明", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String comment;

    @Schema(description = "用户类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "用户类型不能为空")
    private String userType;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    @Schema(description = "用户名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "用户名不能为空")
    private String userName;
}
