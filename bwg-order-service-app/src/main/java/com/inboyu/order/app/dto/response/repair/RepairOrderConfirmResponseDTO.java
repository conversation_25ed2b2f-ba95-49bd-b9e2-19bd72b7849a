package com.inboyu.order.app.dto.response.repair;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 客户端完成确认或取消确认响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "客户端完成确认或取消确认响应")
public class RepairOrderConfirmResponseDTO {

    @Schema(description = "是否成功", example = "true")
    private Boolean success;

    @Schema(description = "响应消息", example = "确认操作成功")
    private String message;

    @Schema(description = "确认类型", example = "confirm")
    private String confirmType;

    @Schema(description = "工作流实例ID", example = "358854085841256448")
    private String workflowInstanceId;

    @Schema(description = "返工说明", example = "突然就需要了")
    private String reworkInstructions;
}
