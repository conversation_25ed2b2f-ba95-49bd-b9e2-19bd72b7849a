package com.inboyu.order.app.application.impl;

import com.inboyu.admin.dto.response.workflow.InstanceDTO;
import com.inboyu.order.app.application.RepairWorkflowAppService;
import com.inboyu.order.app.dto.request.repair.CancelWorkflowRequestDTO;
import com.inboyu.order.app.dto.request.repair.CreateRepairWorkflowRequestDTO;
import com.inboyu.order.app.dto.request.repair.WorkflowApprovalRequestDTO;
import com.inboyu.order.app.dto.request.repair.WorkflowCallbackRequestDTO;
import com.inboyu.order.app.dto.response.repair.CancelWorkflowResponseDTO;
import com.inboyu.order.app.dto.response.repair.WorkflowApprovalResponseDTO;
import com.inboyu.order.domain.repair.service.RepairWorkflowDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 报修工作流应用服务实现
 * 实现报修工作流相关的业务用例
 */
@Slf4j
@Service
public class RepairWorkflowAppServiceImpl implements RepairWorkflowAppService {

    @Autowired
    private RepairWorkflowDomainService repairWorkflowDomainService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createRepairOrderWorkflow(CreateRepairWorkflowRequestDTO dto) {
        log.info("创建报修工单工作流，工单ID: {}", dto.getRepairOrderId());
        
        return repairWorkflowDomainService.createRepairOrderWorkflow(
                dto.getRepairOrderId(),
                dto.getTitle(),
                dto.getDescription(),
                dto.getUserId(),
                dto.getUserName(),
                dto.getStoreId()
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String handleWorkflowCallback(WorkflowCallbackRequestDTO dto) {
        log.info("=== RepairWorkflowAppService.handleWorkflowCallback() 开始处理 ===");
        log.info("文件位置: RepairWorkflowAppServiceImpl.handleWorkflowCallback() - 第45行");
        log.info("开始处理工作流回调通知，实例ID: {}, 业务ID: {}", 
                dto.getInstanceId(), dto.getBusinessId());
        log.info("详细请求参数:");
        log.info("  - 实例ID: {}", dto.getInstanceId());
        log.info("  - 业务ID: {}", dto.getBusinessId());
        log.info("  - 业务类型: {}", dto.getBusinessType());
        log.info("  - 实例状态: {}", dto.getInstanceStatus());
        log.info("  - 节点类型: {}", dto.getNodeType());
        log.info("  - 是否结束: {}", dto.getIsEnd());
        log.info("  - 是否变更实例: {}", dto.getIsChangeInstance());
        log.info("  - 系统备注: {}", dto.getSysRemark());
        log.info("  - 业务变量: {}", dto.getVariable());
        log.info("  - 表单数据: {}", dto.getForm());
        
        try {
            String result = repairWorkflowDomainService.handleWorkflowCallback(
                    dto.getInstanceId(), dto.getBusinessId(), dto.getBusinessType(),
                    dto.getInstanceStatus(), dto.getNodeType(), dto.getIsEnd(),
                    dto.getIsChangeInstance(), dto.getSysRemark(),
                    dto.getVariable(), dto.getForm());
            log.info("工作流回调处理成功，返回结果: {}", result);
            log.info("=== RepairWorkflowAppService.handleWorkflowCallback() 处理完成 ===");
            return result;
        } catch (Exception e) {
            log.error("处理工作流回调通知失败，实例ID: {}, 业务ID: {}, 错误信息: {}", 
                    dto.getInstanceId(), dto.getBusinessId(), e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CancelWorkflowResponseDTO cancelWorkflow(String instanceId, CancelWorkflowRequestDTO dto) {
        log.info("取消工作流，实例ID: {}, 用户: {}", instanceId, dto.getUserName());
        
        String result = repairWorkflowDomainService.cancelWorkflow(
                instanceId, dto.getUserName(), dto.getUserType(), dto.getUserId(), dto.getComment());
        
        CancelWorkflowResponseDTO response = new CancelWorkflowResponseDTO();
        response.setInstanceId(instanceId);
        
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WorkflowApprovalResponseDTO workflowApproval(String instanceStepId, WorkflowApprovalRequestDTO dto) {
        log.info("工作流审批，实例步骤ID: {}, 操作类型: {}, 用户: {}", 
                instanceStepId, dto.getOperateType(), dto.getUserName());
        
        String result = repairWorkflowDomainService.workflowApproval(
                instanceStepId, dto.getOperateType(), dto.getComment(),
                dto.getUserType(), dto.getUserId(), dto.getUserName());
        
        // 这里需要将字符串结果转换为DTO，暂时返回一个简单的响应
        WorkflowApprovalResponseDTO response = new WorkflowApprovalResponseDTO();
        response.setStatus("success");
        return response;
    }

    @Override
    public InstanceDTO getWorkflowInstance(String workflowInstanceId) {
        log.info("查询工作流实例详情，实例ID: {}", workflowInstanceId);
        
        String result = repairWorkflowDomainService.getWorkflowInstance(workflowInstanceId);
        
        // 这里需要将JSON字符串转换为InstanceDTO，暂时返回null
        // TODO: 实现JSON到DTO的转换
        return null;
    }
}
