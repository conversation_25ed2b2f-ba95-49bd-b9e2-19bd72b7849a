package com.inboyu.order.app.dto.request.repair;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * 报修订单DTO
 * <p>
 * //参考 com.inboyu.order.domain.repair.model.RepairOrder的字段
 */
@Data
@Schema(description = "新增报修订单")
public class RepairOrderDTO {

    @Schema(description = "客户ID", example = "358427294957907967", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "客户ID不能为空")
    private String customerId;

    @Schema(description = "门店ID", example = "358427294957907968", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "门店ID不能为空")
    private String storeId;

    @Schema(description = "房间ID", example = "358427294957907969", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "房间ID不能为空")
    private String roomId;

    @Schema(description = "楼栋ID", example = "358427294957907970", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "楼栋ID不能为空")
    private String buildingId;

    @Schema(description = "报修物品ID", example = "358427294957907971", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "报修物品ID不能为空")
    private String itemId;

    @Schema(description = "报修物品故障描述标签", example = "水管漏水", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "报修物品故障描述标签不能为空")
    private String itemType;

    @Schema(description = "工作流ID", example = "358427294957907972", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "工作流ID不能为空")
    private String workflowId;

    @Schema(description = "房号", example = "101", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "房号不能为空")
    private String roomNumber;

    @Schema(description = "报修区域", example = "厨房", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "报修区域不能为空")
    private String area;

    @Schema(description = "报修详情", example = "水管漏水严重，请尽快处理", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "报修详情不能为空")
    private String detail;

    @Schema(description = "联系电话", example = "13800138000", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "联系电话不能为空")
    private String contactPhone;

    @Schema(description = "报修人", example = "张三", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "报修人不能为空")
    private String submitter;

    @Schema(description = "期望报修日期，格式：yyyy-MM-dd", example = "2023-10-01", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "期望报修日期不能为空")
    private LocalDate willRepairDate;

    @Schema(description = "期望报修开始时间，格式：HH:mm", example = "09:00", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "期望报修开始时间不能为空")
    private LocalTime willRepairStart;

    @Schema(description = "期望报修结束时间，格式：HH:mm", example = "12:00", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "期望报修结束时间不能为空")
    private LocalTime willRepairEnd;

    @Schema(description = "是否已入住房间，0-未入住，1-已入住", example = "1", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "是否已入住房间不能为空")
    private Integer isEnter;

    @Schema(description = "报修附件列表")
    @NotBlank(message = "报修附件列表不能为空")
    private List<RepairAttachmentDTO> attachments;

}
