package com.inboyu.order.app.dto.response.repair;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户端获取报修进度响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "客户端获取报修进度响应")
public class RepairOrderProgressResponseDTO {

    @Schema(description = "报修工单ID", example = "123456789")
    private Long repairOrderId;

    @Schema(description = "当前状态", example = "repair_order_status.processing")
    private String currentStatus;

    @Schema(description = "工作流实例ID", example = "358854085841256448")
    private String workflowInstanceId;

    @Schema(description = "工作流详情JSON")
    private String workflowDetail;

    @Schema(description = "进度列表")
    private List<ProgressItemDTO> progressList;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "进度项")
    public static class ProgressItemDTO {
        @Schema(description = "进度ID", example = "987654321")
        private Long progressId;

        @Schema(description = "操作人", example = "张三")
        private String operatorName;

        @Schema(description = "进度描述", example = "您提交了报修，请等待门店受理")
        private String detail;

        @Schema(description = "进度副标题", example = "空调-不制冷：客厅空调不制冷，需要维修")
        private String subDetail;

        @Schema(description = "操作者类型", example = "repair_operator_type.tenant")
        private String operatorType;

        @Schema(description = "处理状态", example = "repair_progress_status.submitted")
        private String status;

        @Schema(description = "备注", example = "已安排维修师傅上门")
        private String comment;

        @Schema(description = "创建时间")
        private LocalDateTime createTime;
    }
}
