package com.inboyu.order.app.dto.request.repair;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

/**
 * 工作流回调请求DTO
 */
@Data
public class WorkflowCallbackRequestDTO {

    @Schema(description = "实例ID")
    private String instanceId;

    @Schema(description = "是否为变更实例")
    private Boolean isChangeInstance;

    @Schema(description = "业务变量")
    private Map<String, String> variable;

    @Schema(description = "表单数据")
    private Map<String, Object> form;

    @Schema(description = "系统备注")
    private String sysRemark;

    @Schema(description = "业务ID")
    private String businessId;

    @Schema(description = "业务类型")
    private String businessType;

    @Schema(description = "节点类型")
    private String nodeType;

    @Schema(description = "实例状态")
    private String instanceStatus;

    @Schema(description = "是否结束")
    private Boolean isEnd;
}
