package com.inboyu.order.app.dto.request.repair;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.time.LocalTime;
import java.util.List;

/**
 * 修改门店报修配置请求DTO
 */
@Data
@Schema(description = "修改门店报修配置")
public class RepairConfigUpdateRequestDTO {

    @Schema(description = "配置ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "配置ID不能为空")
    private String configId;

    @Schema(description = "门店ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "门店ID不能为空")
    private String storeId;

    @Schema(description = "报修开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "报修开始时间不能为空")
    private LocalTime repairStartTime;

    @Schema(description = "报修结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "报修结束时间不能为空")
    private LocalTime repairEndTime;

    @Schema(description = "报修时段时长（分钟）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "报修时段时长不能为空")
    @Min(value = 1, message = "报修时段时长必须大于0")
    @Max(value = 24, message = "报修时段时长不能超过24小时")
    private Integer repairTimeInterval;

    @Schema(description = "每周服务日，多个用逗号分隔", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "每周服务日不能为空")
    private String repairWeekDay;

    @Schema(description = "最远可预约日期（从次日开始算天数）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "最远可预约日期不能为空")
    @Min(value = 1, message = "最远可预约日期必须大于0")
    @Max(value = 30, message = "最远可预约日期不能超过30天")
    private Integer maxRepairDays;

    @Schema(description = "是否开放当天预约", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否开放当天预约不能为空")
    private RepairTodayEnableDTO repairTodayEnable;

    @Schema(description = "可报修物品ID列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "可报修物品ID列表不能为空")
    private List<String> repairItemIds;
}
