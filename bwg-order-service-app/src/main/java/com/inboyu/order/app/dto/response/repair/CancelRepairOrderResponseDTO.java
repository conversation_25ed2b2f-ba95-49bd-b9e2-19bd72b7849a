package com.inboyu.order.app.dto.response.repair;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 客户端取消报修响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "客户端取消报修响应")
public class CancelRepairOrderResponseDTO {

    @Schema(description = "是否成功", example = "true")
    private Boolean success;

    @Schema(description = "响应消息", example = "报修工单取消成功")
    private String message;

    @Schema(description = "工作流实例ID", example = "358854085841256448")
    private String workflowInstanceId;
}
