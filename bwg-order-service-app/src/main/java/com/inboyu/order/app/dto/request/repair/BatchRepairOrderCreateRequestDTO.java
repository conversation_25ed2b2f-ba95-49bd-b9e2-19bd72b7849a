package com.inboyu.order.app.dto.request.repair;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量新增报修订单请求DTO
 */
@Data
@Schema(description = "批量新增报修订单请求")
public class BatchRepairOrderCreateRequestDTO {

    @Schema(description = "报修订单列表")
    @NotEmpty(message = "报修订单列表不能为空")
    @Valid
    private List<RepairOrderCreateRequestDTO> repairOrders;
}
