package com.inboyu.order.app.dto.request.repair;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 工作流审批请求DTO
 */
@Data
public class WorkflowApprovalRequestDTO {

    @Schema(description = "审批意见", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String comment;

    @Schema(description = "操作类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "操作类型不能为空")
    private String operateType;

    @Schema(description = "用户类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "用户类型不能为空")
    private String userType;

    @Schema(description = "操作用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "操作用户ID不能为空")
    private String userId;

    @Schema(description = "用户名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "用户名不能为空")
    private String userName;
}
