package com.inboyu.order.app.dto.request.repair;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 工作流审批请求DTO
 */
@Data
@Schema(description = "工作流审批请求")
public class WorkflowApprovalRequestDTO {

    @Schema(description = "操作类型", example = "pass", allowableValues = {"pass", "reject"})
    @NotBlank(message = "操作类型不能为空")
    private String operateType;

    @Schema(description = "审批备注", example = "同意2222")
    private String comment;

    @Schema(description = "用户类型", example = "user_type.user")
    @NotBlank(message = "用户类型不能为空")
    private String userType;

    @Schema(description = "用户ID", example = "34567876")
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    @Schema(description = "用户姓名", example = "jgygygyg")
    @NotBlank(message = "用户姓名不能为空")
    private String userName;
}