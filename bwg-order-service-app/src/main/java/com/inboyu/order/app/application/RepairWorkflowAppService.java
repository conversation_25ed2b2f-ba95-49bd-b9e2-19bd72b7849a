package com.inboyu.order.app.application;

import com.inboyu.admin.dto.response.workflow.InstanceDTO;
import com.inboyu.order.app.dto.request.repair.CancelWorkflowRequestDTO;
import com.inboyu.order.app.dto.request.repair.CreateRepairWorkflowRequestDTO;
import com.inboyu.order.app.dto.request.repair.WorkflowApprovalRequestDTO;
import com.inboyu.order.app.dto.request.repair.WorkflowCallbackRequestDTO;
import com.inboyu.order.app.dto.response.repair.CancelWorkflowResponseDTO;
import com.inboyu.order.app.dto.response.repair.WorkflowApprovalResponseDTO;

/**
 * 报修工作流应用服务接口
 * 定义报修工作流相关的业务用例
 */
public interface RepairWorkflowAppService {

    /**
     * 创建报修工单工作流
     *
     * @param dto 创建请求DTO
     * @return 工作流实例ID
     */
    String createRepairOrderWorkflow(CreateRepairWorkflowRequestDTO dto);

    /**
     * 处理工作流回调通知
     *
     * @param dto 回调请求DTO
     * @return 处理结果
     */
    String handleWorkflowCallback(WorkflowCallbackRequestDTO dto);

    /**
     * 取消工作流
     *
     * @param instanceId 工作流实例ID
     * @param dto 取消请求DTO
     * @return 取消结果
     */
    CancelWorkflowResponseDTO cancelWorkflow(String instanceId, CancelWorkflowRequestDTO dto);

    /**
     * 工作流审批
     *
     * @param instanceStepId 实例步骤ID
     * @param dto 审批请求DTO
     * @return 审批结果
     */
    WorkflowApprovalResponseDTO workflowApproval(String instanceStepId, WorkflowApprovalRequestDTO dto);

    /**
     * 查询工作流实例详情
     *
     * @param workflowInstanceId 工作流实例ID
     * @return 工作流实例详情
     */
    InstanceDTO getWorkflowInstance(String workflowInstanceId);
}
