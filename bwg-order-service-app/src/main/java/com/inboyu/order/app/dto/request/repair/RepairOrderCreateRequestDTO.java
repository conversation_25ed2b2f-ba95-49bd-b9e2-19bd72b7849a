package com.inboyu.order.app.dto.request.repair;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * 新增报修订单请求DTO
 */
@Data
@Schema(description = "新增报修订单请求")
public class RepairOrderCreateRequestDTO {

    @Schema(description = "客户ID", example = "356773268285620224")
    @NotNull(message = "客户ID不能为空")
    @Positive(message = "客户ID必须大于0")
    private String customerId;

    @Schema(description = "门店ID", example = "346117920746987520")
    @NotNull(message = "门店ID不能为空")
    @Positive(message = "门店ID必须大于0")
    private String storeId;

    @Schema(description = "房间ID", example = "350956905650556928")
    @NotNull(message = "房间ID不能为空")
    @Positive(message = "房间ID必须大于0")
    private String roomId;

    @Schema(description = "楼栋ID", example = "350898137600450560")
    @NotNull(message = "楼栋ID不能为空")
    @Positive(message = "楼栋ID必须大于0")
    private String buildingId;

    @Schema(description = "报修物品ID", example = "202509120000000078")
    @NotNull(message = "报修物品ID不能为空")
    @Positive(message = "报修物品ID必须大于0")
    private String itemId;

    @Schema(description = "报修物品名称", example = "热水器")
    @NotNull(message = "报修物品名称不能为空")
    private String itemName;

    @Schema(description = "报修物品故障描述标签", example = "水龙头漏水")
    @NotBlank(message = "报修物品故障描述标签不能为空")
    private String itemTag;

    @Schema(description = "工作流ID", example = "6001")
    private String workflowId;

    @Schema(description = "房号", example = "A-1001")
    @NotBlank(message = "房号不能为空")
    private String roomNumber;

    @Schema(description = "报修区域", example = "卫生间")
    @NotBlank(message = "报修区域不能为空")
    private String area;

    @Schema(description = "问题描述", example = "卫生间水龙头持续滴水，影响正常使用")
    @NotBlank(message = "问题描述不能为空")
    private String detail;

    @Schema(description = "联系电话", example = "13800138000")
    @NotBlank(message = "联系电话不能为空")
    private String contactPhone;

    @Schema(description = "报修人姓名", example = "张三")
    @NotBlank(message = "报修人姓名不能为空")
    private String submitter;

    @Schema(description = "意向维修日期", example = "2025-09-20")
    private LocalDate willRepairDate;

    @Schema(description = "意向维修开始时间段", example = "09:00:00")
    private LocalTime willRepairStart;

    @Schema(description = "意向维修结束时间段", example = "17:00:00")
    private LocalTime willRepairEnd;

    @Schema(description = "是否无人可直接入户，0-否 1-是", example = "0")
    private Integer isEnter;

    @Schema(description = "备注", example = "请在工作时间联系")
    private String remark;

    @Schema(description = "报修附件列表")
    @Valid
    private List<RepairAttachmentCreateRequestDTO> attachments;
}
