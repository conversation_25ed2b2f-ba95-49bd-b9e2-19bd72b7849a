package com.inboyu.order.app.dto.response.repair;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 报修物品响应DTO
 */
@Data
@Schema(description = "报修物品")
public class RepairItemResponseDTO {

    @Schema(description = "物品ID")
    private String itemId;

    @Schema(description = "物品分类")
    private String kind;

    @Schema(description = "物品名称")
    private String name;

    @Schema(description = "物品图标")
    private String icon;

    @Schema(description = "Logo地址")
    private String url;

    @Schema(description = "故障描述标签")
    private String remark;

    @Schema(description = "排序")
    private Integer sortOrder;

    @Schema(description = "是否启用")
    private Boolean isActive;
}
