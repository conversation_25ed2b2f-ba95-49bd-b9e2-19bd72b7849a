package com.inboyu.order.app.dto.response.repair;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 批量新增报修订单响应DTO
 */
@Data
@Schema(description = "批量新增报修订单响应")
public class BatchRepairOrderCreateResponseDTO {

    @Schema(description = "成功创建的报修订单列表")
    private List<RepairOrderResponseDTO> successOrders;

    @Schema(description = "成功数量", example = "8")
    private Integer successCount;

}
