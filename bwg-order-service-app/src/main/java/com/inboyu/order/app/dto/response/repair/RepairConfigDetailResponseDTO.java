package com.inboyu.order.app.dto.response.repair;

import com.inboyu.order.app.dto.request.repair.RepairTodayEnableDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalTime;
import java.util.List;

/**
 * 门店报修配置详情响应DTO
 */
@Data
@Schema(description = "门店报修配置详情")
public class RepairConfigDetailResponseDTO {

    @Schema(description = "配置ID")
    private String configId;

    @Schema(description = "门店ID")
    private String storeId;

    @Schema(description = "报修开始时间")
    private LocalTime repairStartTime;

    @Schema(description = "报修结束时间")
    private LocalTime repairEndTime;

    @Schema(description = "报修时段时长（分钟）")
    private Integer repairTimeInterval;

    @Schema(description = "每周服务日")
    private String repairWeekDay;

    @Schema(description = "最远可预约日期（从次日开始算天数）")
    private Integer maxRepairDays;

    @Schema(description = "是否开放当天预约")
    private RepairTodayEnableDTO repairTodayEnable;

    @Schema(description = "可报修物品列表")
    private List<RepairItemResponseDTO> repairItems;
}
