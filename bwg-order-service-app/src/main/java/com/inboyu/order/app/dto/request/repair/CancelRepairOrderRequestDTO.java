package com.inboyu.order.app.dto.request.repair;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 客户端取消报修请求DTO
 */
@Data
@Schema(description = "客户端取消报修请求")
public class CancelRepairOrderRequestDTO {

    @Schema(description = "报修工单ID", example = "123456789")
    @NotNull(message = "报修工单ID不能为空")
    private Long repairOrderId;

    @Schema(description = "取消原因", example = "问题已自行解决")
    @NotBlank(message = "取消原因不能为空")
    private String cancelReason;
}
