package com.inboyu.order.app.dto.response.repair;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 报修进度响应DTO
 */
@Data
public class RepairProgressResponseDTO {

    /**
     * 进度ID
     */
    private Long progressId;

    /**
     * 报修工单ID
     */
    private Long repairOrderId;

    /**
     * 操作用户ID
     */
    private Long userId;

    /**
     * 操作用户名
     */
    private String userName;

    /**
     * 进度描述
     */
    private String detail;

    /**
     * 操作者类型：1-租客 2-管家 3-店长 4-系统
     */
    private Integer type;

    /**
     * 处理状态：1-已提单 2-处理中 3-已完成 4-已关闭
     */
    private Integer status;

    /**
     * 交接、完成备注
     */
    private String comment;

    /**
     * 派单原因
     */
    private String distributeReason;

    /**
     * 派单标签
     */
    private String distributeDetail;

    /**
     * 是否重开操作
     */
    private Boolean isReopen;

    /**
     * 重开原因
     */
    private String reopenReason;

    /**
     * 处理方式：0-自受理 1-转派
     */
    private Integer processMethod;

    /**
     * 转派目标处理人ID
     */
    private Long assigneeId;

    /**
     * 转派目标处理人姓名
     */
    private String assigneeName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
