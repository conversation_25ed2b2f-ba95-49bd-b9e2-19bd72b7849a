package com.inboyu.order.app.dto.converter;

import com.inboyu.order.app.dto.request.repair.RepairAttachmentCreateRequestDTO;
import com.inboyu.order.app.dto.request.repair.RepairOrderCreateRequestDTO;
import com.inboyu.order.app.dto.response.repair.RepairAttachmentResponseDTO;
import com.inboyu.order.app.dto.response.repair.RepairOrderPageListResponseDTO;
import com.inboyu.order.app.dto.response.repair.RepairOrderResponseDTO;
import com.inboyu.order.domain.repair.model.*;
import com.inboyu.order.domain.repair.repository.RepairOrderRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 报修订单数据转换器
 */
@Component
public class RepairOrderConverter {

    @Autowired
    private RepairOrderRepository repairOrderRepository;

    /**
     * 将请求DTO转换为领域对象
     */
    public RepairOrder toDomain(RepairOrderCreateRequestDTO requestDTO) {
        // 生成订单ID
        RepairOrderId repairOrderId = repairOrderRepository.generateId();

        // 转换附件
        List<RepairAttachment> attachments = null;
        if (requestDTO.getAttachments() != null) {
            attachments = requestDTO.getAttachments().stream()
                    .map(this::toAttachmentDomain)
                    .collect(Collectors.toList());
        }

        return RepairOrder.builder()
                .repairOrderId(repairOrderId)
                .customerId(CustomerId.of(requestDTO.getCustomerId()))
                .storeId(StoreId.of(requestDTO.getStoreId()))
                .roomId(RoomId.of(requestDTO.getRoomId()))
                .buildingId(BuildingId.of(requestDTO.getBuildingId()))
                .itemId(ItemId.of(requestDTO.getItemId()))
                .itemTag(requestDTO.getItemTag())
                .workflowId(WorkflowId.of(requestDTO.getWorkflowId()))
                .roomNumber(requestDTO.getRoomNumber())
                .area(requestDTO.getArea())
                .detail(requestDTO.getDetail())
                .contactPhone(requestDTO.getContactPhone())
                .submitter(requestDTO.getSubmitter())
                .willRepairDate(requestDTO.getWillRepairDate())
                .willRepairStart(requestDTO.getWillRepairStart())
                .willRepairEnd(requestDTO.getWillRepairEnd())
                .isEnter(IsEnter.of(requestDTO.getIsEnter()))
                .status(RepairOrderStatus.of(RepairOrderStatus.REPAIR_ORDER_STATUS_SUBMITTED))
                .orderType(OrderType.of(OrderType.REPAIR_ORDER_TYPE_RESIDENT))
                .tagStatus(TagStatus.of(TagStatus.REPAIR_TAG_STATUS_NORMAL))
                .refuseReason("")
                .suspendReason("")
                .responsibleId(ResponsibleId.of(0L))
                .maintainerId(MaintainerId.of(0L))
                .remark(requestDTO.getRemark() != null ? requestDTO.getRemark() : "")
                .attachments(attachments)
                .build();
    }

    /**
     * 将领域对象转换为响应DTO
     */
    public RepairOrderResponseDTO toResponseDTO(RepairOrder repairOrder) {
        RepairOrderResponseDTO responseDTO = new RepairOrderResponseDTO();
        responseDTO.setRepairOrderId(repairOrder.getRepairOrderId().toString());
        responseDTO.setCustomerId(repairOrder.getCustomerId().toString());
        responseDTO.setStoreId(repairOrder.getStoreId().toString());
        responseDTO.setRoomId(repairOrder.getRoomId().toString());
        responseDTO.setBuildingId(repairOrder.getBuildingId().toString());
        responseDTO.setItemId(repairOrder.getItemId().toString());
        responseDTO.setItemTag(repairOrder.getItemTag());
        responseDTO.setWorkflowId(repairOrder.getWorkflowId().toString());
        responseDTO.setRoomNumber(repairOrder.getRoomNumber());
        responseDTO.setArea(repairOrder.getArea());
        responseDTO.setDetail(repairOrder.getDetail());
        responseDTO.setContactPhone(repairOrder.getContactPhone());
        responseDTO.setSubmitter(repairOrder.getSubmitter());
        responseDTO.setWillRepairDate(repairOrder.getWillRepairDate());
        responseDTO.setWillRepairStart(repairOrder.getWillRepairStart());
        responseDTO.setWillRepairEnd(repairOrder.getWillRepairEnd());
        responseDTO.setIsEnter(repairOrder.getIsEnter().getValue());
        responseDTO.setStatus(repairOrder.getStatus().getCode());
        responseDTO.setOrderType(repairOrder.getOrderType().getCode());
        responseDTO.setTagStatus(repairOrder.getTagStatus().getCode());
        responseDTO.setRemark(repairOrder.getRemark());
        responseDTO.setCreateTime(LocalDateTime.now());

        // 转换附件
        if (repairOrder.getAttachments() != null) {
            List<RepairAttachmentResponseDTO> attachmentDTOs = repairOrder.getAttachments().stream()
                    .map(this::toAttachmentResponseDTO)
                    .collect(Collectors.toList());
            responseDTO.setAttachments(attachmentDTOs);
        }

        return responseDTO;
    }

    /**
     * 将领域对象转换为分页列表响应DTO
     */
    public RepairOrderPageListResponseDTO toPageListResponseDTO(RepairOrder repairOrder) {
        RepairOrderPageListResponseDTO responseDTO = new RepairOrderPageListResponseDTO();
        responseDTO.setRepairOrderId(repairOrder.getRepairOrderId().getValue().toString());
        responseDTO.setCustomerId(repairOrder.getCustomerId().getValue().toString());
        responseDTO.setStoreId(repairOrder.getStoreId().getValue().toString());
        responseDTO.setRoomId(repairOrder.getRoomId().getValue().toString());
        responseDTO.setBuildingId(repairOrder.getBuildingId().getValue().toString());
        responseDTO.setItemId(repairOrder.getItemId().getValue().toString());
        responseDTO.setItemTag(repairOrder.getItemTag());
        responseDTO.setWorkflowId(repairOrder.getWorkflowId().getValue());
        responseDTO.setRoomNumber(repairOrder.getRoomNumber());
        responseDTO.setArea(repairOrder.getArea());
        responseDTO.setDetail(repairOrder.getDetail());
        responseDTO.setContactPhone(repairOrder.getContactPhone());
        responseDTO.setSubmitter(repairOrder.getSubmitter());
        responseDTO.setWillRepairDate(repairOrder.getWillRepairDate());
        responseDTO.setWillRepairStart(repairOrder.getWillRepairStart());
        responseDTO.setWillRepairEnd(repairOrder.getWillRepairEnd());
        responseDTO.setIsEnter(repairOrder.getIsEnter().getValue());
        responseDTO.setStatus(repairOrder.getStatus().getCode());
        responseDTO.setOrderType(repairOrder.getOrderType().getCode());
        responseDTO.setTagStatus(repairOrder.getTagStatus().getCode());
        responseDTO.setRemark(repairOrder.getRemark());
        return responseDTO;
    }

    /**
     * 将附件请求DTO转换为领域对象
     */
    private RepairAttachment toAttachmentDomain(RepairAttachmentCreateRequestDTO requestDTO) {
        AttachmentId attachmentId = repairOrderRepository.generateAttachmentId();

        return RepairAttachment.builder()
                .attachmentId(attachmentId)
                .fileName(requestDTO.getFileName())
                .fileUrl(requestDTO.getFileUrl())
                .fileType(requestDTO.getFileType())
                .attachmentType(requestDTO.getAttachmentType())
                .fileSize(requestDTO.getFileSize())
                .uploadTime(LocalDateTime.now())
                .build();
    }

    /**
     * 将附件领域对象转换为响应DTO
     */
    private RepairAttachmentResponseDTO toAttachmentResponseDTO(RepairAttachment attachment) {
        RepairAttachmentResponseDTO responseDTO = new RepairAttachmentResponseDTO();
        responseDTO.setAttachmentId(attachment.getAttachmentId().toString());
        responseDTO.setFileName(attachment.getFileName());
        responseDTO.setFileUrl(attachment.getFileUrl());
        responseDTO.setFileType(attachment.getFileType());
        responseDTO.setAttachmentType(attachment.getAttachmentType());
        responseDTO.setFileSize(attachment.getFileSize());
        responseDTO.setUploadTime(attachment.getUploadTime());
        return responseDTO;
    }


}
