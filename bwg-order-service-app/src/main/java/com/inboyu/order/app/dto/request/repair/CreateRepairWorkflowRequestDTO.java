package com.inboyu.order.app.dto.request.repair;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 创建报修工作流请求DTO
 */
@Data
public class CreateRepairWorkflowRequestDTO {

    @Schema(description = "报修工单ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "报修工单ID不能为空")
    private String repairOrderId;

    @Schema(description = "工作流标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "工作流标题不能为空")
    private String title;

    @Schema(description = "工作流描述", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "工作流描述不能为空")
    private String description;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    @Schema(description = "用户名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "用户名不能为空")
    private String userName;

    @Schema(description = "门店ID", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
    private String storeId;
}
