package com.inboyu.order.app.application;

import com.inboyu.order.app.dto.request.repair.BatchRepairOrderCreateRequestDTO;
import com.inboyu.order.app.dto.request.repair.CancelRepairOrderRequestDTO;
import com.inboyu.order.app.dto.request.repair.RepairOrderAcceptRejectRequestDTO;
import com.inboyu.order.app.dto.request.repair.RepairOrderCompleteRequestDTO;
import com.inboyu.order.app.dto.request.repair.RepairOrderConfirmRequestDTO;
import com.inboyu.order.app.dto.request.repair.RepairOrderProgressRequestDTO;
import com.inboyu.order.app.dto.response.repair.*;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import com.inboyu.order.app.dto.response.repair.BatchRepairOrderCreateResponseDTO;
import com.inboyu.order.app.dto.response.repair.CancelRepairOrderResponseDTO;
import com.inboyu.order.app.dto.response.repair.RepairOrderAcceptRejectResponseDTO;
import com.inboyu.order.app.dto.response.repair.RepairOrderCompleteResponseDTO;
import com.inboyu.order.app.dto.response.repair.RepairOrderConfirmResponseDTO;
import com.inboyu.order.app.dto.response.repair.RepairOrderProgressResponseDTO;

/**
 * 报修订单应用服务接口
 */
public interface RepairOrderAppService {

    /**
     * 批量创建报修订单
     *
     * @param request 批量创建请求
     * @return 批量创建响应
     */
    BatchRepairOrderCreateResponseDTO batchCreateRepairOrders(BatchRepairOrderCreateRequestDTO request);

    /**
     * 客户端取消报修
     *
     * @param request 取消报修请求
     * @return 取消报修响应
     */
    CancelRepairOrderResponseDTO cancelRepairOrder(CancelRepairOrderRequestDTO request);

    /**
     * 客户端获取报修进度
     *
     * @param request 获取进度请求
     * @return 报修进度响应
     */
    RepairOrderProgressResponseDTO getRepairOrderProgress(RepairOrderProgressRequestDTO request);

    /**
     * 客户端完成确认或取消确认
     *
     * @param request 确认请求
     * @return 确认响应
     */
    RepairOrderConfirmResponseDTO confirmRepairOrder(RepairOrderConfirmRequestDTO request);

    /**
     * 运营端-报修订单分页列表
     *
     * @param pageNum       当前页
     * @param pageSize      每页条数
     * @param customerId    客户ID
     * @param storeId       门店ID
     * @param status        订单状态
     * @param responsibleId 负责人ID
     * @param roomNum       房间号
     * @param roomId        房间ID
     * @return 分页结果
     */
    Pagination<RepairOrderPageListResponseDTO> pageRepairOrder(
            Integer pageNum, Integer pageSize,
            String customerId, String storeId, String status,
            String responsibleId, String roomNum, String roomId);

    /**
     * 运营端受理/不受理报修
     *
     * @param request 受理/不受理请求
     * @return 受理/不受理响应
     */
    RepairOrderAcceptRejectResponseDTO acceptRejectRepairOrder(RepairOrderAcceptRejectRequestDTO request);

    /**
     * 运营端完成维修
     *
     * @param request 完成维修请求
     * @return 完成维修响应
     */
    RepairOrderCompleteResponseDTO completeRepairOrder(RepairOrderCompleteRequestDTO request);
}
