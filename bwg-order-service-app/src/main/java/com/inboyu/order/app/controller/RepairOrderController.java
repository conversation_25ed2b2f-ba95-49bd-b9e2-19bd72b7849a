package com.inboyu.order.app.controller;

import com.inboyu.order.app.application.RepairOrderAppService;
import com.inboyu.order.app.dto.request.repair.RepairOrderAcceptRejectRequestDTO;
import com.inboyu.order.app.dto.request.repair.RepairOrderCompleteRequestDTO;
import com.inboyu.order.app.dto.request.repair.RepairOrderProgressRequestDTO;
import com.inboyu.order.app.dto.response.repair.RepairOrderAcceptRejectResponseDTO;
import com.inboyu.order.app.dto.response.repair.RepairOrderCompleteResponseDTO;
import com.inboyu.order.app.dto.request.repair.BatchRepairOrderCreateRequestDTO;
import com.inboyu.order.app.dto.response.repair.BatchRepairOrderCreateResponseDTO;
import com.inboyu.order.app.dto.response.repair.RepairOrderPageListResponseDTO;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import com.inboyu.order.app.dto.response.repair.RepairOrderProgressResponseDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 运营端报修订单控制器
 * 提供运营端报修相关的接口
 */
@Slf4j
@RestController("adminRepairOrderController")
@RequestMapping("/api/v1/repair-order")
@Tag(name = "报修订单-运营端", description = "运营端报修订单接口")
@Validated
public class RepairOrderController {

    @Autowired
    private  RepairOrderAppService repairOrderAppService;

    /**
     * 运营端-报修订单分页列表
     */
    @GetMapping
    @Operation(summary = "运营端-报修订单分页列表", description = "运营端-报修单详情")
    public Pagination<RepairOrderPageListResponseDTO> pageRepairOrder(
            @RequestParam @Schema(description = "当前页") Integer pageNum,
            @RequestParam @Schema(description = "每页条数") Integer pageSize,
            @RequestParam(required = false) @Schema(description = "客户ID") String customerId,
            @RequestParam(required = false) @Schema(description = "门店ID") String storeId,
            @RequestParam(required = false) @Schema(description = "报修状态") String status,
            @RequestParam(required = false) @Schema(description = "负责人id") String responsibleId,
            @RequestParam(required = false) @Schema(description = "房间号") String roomNum,
            @RequestParam(required = false) @Schema(description = "房间ID") String roomId) {
        return repairOrderAppService.pageRepairOrder(pageNum, pageSize, customerId, storeId, status, responsibleId, roomNum, roomId);
    }

    /**
     * 批量创建报修订单
     */
    @Operation(summary = "运营端-批量创建报修订单", description = "支持一次提交多个报修订单，每个订单可包含多个附件")
    @PostMapping("/batch")
    public BatchRepairOrderCreateResponseDTO batchCreateRepairOrders(
            @Valid @RequestBody BatchRepairOrderCreateRequestDTO request) {

        log.info("接收到批量创建报修订单请求，订单数量：{}", request.getRepairOrders().size());
        return repairOrderAppService.batchCreateRepairOrders(request);
    }

    /**
     * 运营端受理/不受理报修
     */
    @Operation(summary = "运营端-受理/不受理报修", description = "运营端对报修工单进行受理或不受理操作，调用工作流审批")
    @PostMapping("/accept-reject")
    public RepairOrderAcceptRejectResponseDTO acceptRejectRepairOrder(
            @Valid @RequestBody RepairOrderAcceptRejectRequestDTO request) {
        log.info("运营端受理/不受理报修，订单ID：{}，操作类型：{}", request.getRepairOrderId(), request.getOperationType());
        return repairOrderAppService.acceptRejectRepairOrder(request);
    }

    /**
     * 运营端完成维修
     */
    @Operation(summary = "运营端-完成维修", description = "运营端完成维修操作，更新报修工单状态")
    @PostMapping("/complete")
    public RepairOrderCompleteResponseDTO completeRepairOrder(
            @Valid @RequestBody RepairOrderCompleteRequestDTO request) {
        log.info("运营端完成维修，订单ID：{}，维修人员：{}", request.getRepairOrderId(), request.getMaintainerName());
        return repairOrderAppService.completeRepairOrder(request);
    }

    /**
     * 运营端获取报修进度
     */
    @Operation(summary = "运营端获取报修进度", description = "运营端获取报修工单的详细进度信息")
    @PostMapping("/progress")
    public RepairOrderProgressResponseDTO getRepairOrderProgress(
            @Valid @RequestBody RepairOrderProgressRequestDTO request) {
        log.info("运营端获取报修进度，订单ID：{}", request.getRepairOrderId());
        return repairOrderAppService.getRepairOrderProgress(request);
    }
}
