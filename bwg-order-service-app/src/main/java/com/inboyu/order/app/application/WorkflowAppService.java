package com.inboyu.order.app.application;

import com.inboyu.admin.dto.request.workflow.*;
import com.inboyu.admin.dto.response.workflow.*;

/**
 * 工作流应用服务接口
 */
public interface WorkflowAppService {

    /**
     * 创建工作流实例
     * 
     * @param request 创建请求参数
     * @return 创建结果
     */
    InstanceCreateResponseDTO createWorkflow(InstanceCreateRequestDTO request);

    /**
     * 审批工作流
     * 
     * @param instanceStepId 实例步骤ID
     * @param request 审批请求参数
     * @return 审批结果
     */
    InstanceStepBasicDTO approveWorkflow(String instanceStepId, InstanceApprovalRequestDTO request);

    /**
     * 撤销工作流
     * 
     * @param instanceId 实例ID
     * @param request 撤销请求参数
     * @return 撤销结果
     */
    InstanceCancelResponseDTO cancelWorkflow(String instanceId, InstanceCancelRequestDTO request);

    /**
     * 查询工作流实例
     * 
     * @param workflowInstanceId 工作流实例ID
     * @return 工作流实例信息
     */
    InstanceDTO getWorkflowInstance(String workflowInstanceId);

    /**
     * 预览工作流实例
     * 
     * @param request 预览请求参数
     * @return 预览结果
     */
    //InstancePreviewResponseDTO previewWorkflow(InstancePreviewRequestDTO request);

    /**
     * 新增工作流步骤
     * 
     * @param workflowInstanceId 工作流实例ID
     * @param request 新增步骤请求参数
     * @return 新增步骤结果
     */
    //InstanceNewStepDTO newWorkflowStep(String workflowInstanceId, InstanceNewStepRequestDTO request);
}