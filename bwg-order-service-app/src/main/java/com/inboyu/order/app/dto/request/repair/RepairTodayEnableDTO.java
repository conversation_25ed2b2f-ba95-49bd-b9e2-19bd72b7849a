package com.inboyu.order.app.dto.request.repair;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 当天报修开放状态DTO
 */
@Data
@Schema(description = "当天报修开放状态，查询字典获取")
public class RepairTodayEnableDTO {

    @Schema(description = "状态编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "状态编码不能为空")
    private String code;

    @Schema(description = "状态标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "状态标题不能为空")
    private String title;
}
