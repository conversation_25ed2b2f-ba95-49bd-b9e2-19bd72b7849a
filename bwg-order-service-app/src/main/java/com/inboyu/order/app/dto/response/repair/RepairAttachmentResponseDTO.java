package com.inboyu.order.app.dto.response.repair;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 报修附件响应DTO
 */
@Data
public class RepairAttachmentResponseDTO {

    /**
     * 附件ID
     */
    private Long attachmentId;

    /**
     * 报修工单ID
     */
    private Long repairOrderId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件URL
     */
    private String fileUrl;

    /**
     * 文件类型：1-图片 2-视频 3-文档
     */
    private Integer fileType;

    /**
     * 附件类型：1-报修图片 2-报修视频 3-进度图片 4-进度视频 5-挂起图片
     */
    private Integer attachmentType;

    /**
     * 文件大小(字节)
     */
    private Long fileSize;

    /**
     * 上传时间
     */
    private LocalDateTime uploadTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
