package com.inboyu.order.app.dto.request.repair;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 报修报修附件DTO
 */
@Data
@Schema(description = "新增报修附件")
public class RepairAttachmentDTO {

    @Schema(description = "文件名", example = "repair_image_01.jpg", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "文件名不能为空")
    private String fileName;

    @Schema(description = "文件URL", example = "https://example.com/repair_image_01.jpg", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "文件URL不能为空")
    private String fileUrl;

    @Schema(description = "文件类型", example = "repair_file_type.image", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "文件类型不能为空")
    private String fileType;

    @Schema(description = "附件类型", example = "repair_attachment_type.repair_image", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "附件类型不能为空")
    private String attachmentType;

    @Schema(description = "文件大小（字节）", example = "204800", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "文件大小不能为空")
    private Long fileSize;

    @Schema(description = "上传时间", example = "14:30:00", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "上传时间不能为空")
    private String uploadTime;

}
