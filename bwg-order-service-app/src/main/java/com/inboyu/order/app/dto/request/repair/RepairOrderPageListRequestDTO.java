package com.inboyu.order.app.dto.request.repair;

import io.swagger.v3.oas.annotations.media.Schema;

public class RepairOrderPageListRequestDTO {

    @Schema(description = "客户ID", example = "35433534535535")
    private String customerId;

    @Schema(description = "门店ID", example = "203534534345301")
    private String storeId;

    @Schema(description = "房间ID", example = "354353454335")
    private String roomId;

    @Schema(description = "房号", example = "A-1001")
    private String roomNumber;

    @Schema(description = "负责人ID", example = "34534535345435")
    private String responsibleId;

    @Schema(description = "报修订单状态，多个状态用逗号分隔，如：PENDING,IN_PROGRESS,COMPLETED", example = "PENDING,IN_PROGRESS")
    private String repairOrderStatus;


}
