package com.inboyu.order.app.controller;

import com.inboyu.admin.dto.response.workflow.InstanceDTO;
import com.inboyu.order.app.application.impl.RepairWorkflowService;
import com.inboyu.order.app.dto.request.repair.CancelWorkflowRequestDTO;
import com.inboyu.order.app.dto.request.repair.CreateRepairWorkflowRequestDTO;
import com.inboyu.order.app.dto.request.repair.WorkflowApprovalRequestDTO;
import com.inboyu.order.app.dto.request.repair.WorkflowCallbackRequestDTO;
import com.inboyu.order.app.dto.response.repair.CancelWorkflowResponseDTO;
import com.inboyu.order.app.dto.response.repair.WorkflowApprovalResponseDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 报修工作流控制器
 * 提供报修相关的工作流操作接口
 *
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/repair/workflow")
@Tag(name = "报修工作流管理")
public class RepairWorkflowController {

    @Autowired
    private RepairWorkflowService repairWorkflowService;

    /**
     * 创建报修工单工作流
     */
    @PostMapping("/create")
    @Operation(summary = "创建报修工单工作流")
    public String createRepairOrderWorkflow(@RequestBody @Valid CreateRepairWorkflowRequestDTO request) {
        return repairWorkflowService.createRepairOrderWorkflow(
                request.getRepairOrderId(),
                request.getTitle(),
                request.getDescription(),
                request.getUserId(),
                request.getUserName(),
                request.getStoreId()
        );
    }

    /**
     * 工作流回调接口
     * 接收工作流状态变更通知
     */
    @PostMapping("/callback")
    @Operation(summary = "工作流回调通知")
    public String workflowCallback(@RequestBody @Valid WorkflowCallbackRequestDTO request) {
        log.info("收到工作流回调通知，实例ID: {}, 业务ID: {}, 状态: {}, 是否结束: {}", 
                request.getInstanceId(), request.getBusinessId(), 
                request.getInstanceStatus(), request.getIsEnd());
        
        return repairWorkflowService.handleWorkflowCallback(request);
    }

    /**
     * 取消工作流
     */
    @PostMapping("/{instanceId}/cancel")
    @Operation(summary = "取消工作流")
    public CancelWorkflowResponseDTO cancelWorkflow(
            @Parameter(description = "工作流实例ID") @PathVariable String instanceId,
            @RequestBody @Valid CancelWorkflowRequestDTO request) {
        log.info("收到取消工作流请求，实例ID: {}, 用户: {}", instanceId, request.getUserName());
        
        String result = repairWorkflowService.cancelWorkflow(instanceId, request);
        
        CancelWorkflowResponseDTO response = new CancelWorkflowResponseDTO();
        response.setInstanceId(instanceId);
        
        return response;
    }

    /**
     * 工作流审批接口
     */
    @PostMapping("/{instanceStepId}/approval")
    @Operation(summary = "工作流审批")
    public WorkflowApprovalResponseDTO workflowApproval(
            @Parameter(description = "实例步骤ID") @PathVariable String instanceStepId,
            @RequestBody @Valid WorkflowApprovalRequestDTO request) {
        log.info("收到工作流审批请求，实例步骤ID: {}, 操作类型: {}, 用户: {}", 
                instanceStepId, request.getOperateType(), request.getUserName());
        
        return repairWorkflowService.workflowApproval(instanceStepId, request);
    }

    /**
     * 查询工作流实例详情
     */
    @GetMapping("/{workflowInstanceId}")
    @Operation(summary = "查询工作流实例详情")
    public InstanceDTO getWorkflowInstance(
            @Parameter(description = "工作流实例ID") @PathVariable String workflowInstanceId) {
        log.info("收到查询工作流实例详情请求，实例ID: {}", workflowInstanceId);
        
        return repairWorkflowService.getWorkflowInstance(workflowInstanceId);
    }

}

