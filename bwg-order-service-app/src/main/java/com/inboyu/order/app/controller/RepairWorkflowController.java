package com.inboyu.order.app.controller;

import com.inboyu.admin.dto.response.workflow.InstanceDTO;
import com.inboyu.order.app.application.RepairWorkflowAppService;
import com.inboyu.order.app.dto.request.repair.CancelWorkflowRequestDTO;
import com.inboyu.order.app.dto.request.repair.CreateRepairWorkflowRequestDTO;
import com.inboyu.order.app.dto.request.repair.WorkflowApprovalRequestDTO;
import com.inboyu.order.app.dto.request.repair.WorkflowCallbackRequestDTO;
import com.inboyu.order.app.dto.response.repair.CancelWorkflowResponseDTO;
import com.inboyu.order.app.dto.response.repair.WorkflowApprovalResponseDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 报修工作流控制器
 * 提供报修相关的工作流操作接口
 *
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/repair/workflow")
@Tag(name = "报修工作流管理")
public class RepairWorkflowController {

    @Autowired
    private RepairWorkflowAppService repairWorkflowAppService;

    /**
     * 创建报修工单工作流
     *
     * @param dto 创建请求DTO
     * @return 工作流实例ID
     */
    @PostMapping("/create")
    @Operation(summary = "创建报修工单工作流", description = "创建报修工单工作流")
    public String createRepairOrderWorkflow(@Valid @RequestBody CreateRepairWorkflowRequestDTO dto) {
        log.info("创建报修工单工作流，工单ID: {}", dto.getRepairOrderId());
        return repairWorkflowAppService.createRepairOrderWorkflow(dto);
    }

    /**
     * 工作流回调接口
     * 接收工作流状态变更通知
     *
     * @param dto 回调请求DTO
     * @return 处理结果
     */
    @PostMapping("/callback")
    @Operation(summary = "工作流回调通知", description = "接收工作流状态变更通知")
    public String workflowCallback(@Valid @RequestBody WorkflowCallbackRequestDTO dto) {
        log.info("=== 工作流回调接口开始处理 ===");
        log.info("文件位置: RepairWorkflowController.workflowCallback() - 第55行");
        log.info("请求URL: POST /api/v1/repair/workflow/callback");
        log.info("实例ID: {}", dto.getInstanceId());
        log.info("业务ID: {}", dto.getBusinessId());
        log.info("业务类型: {}", dto.getBusinessType());
        log.info("实例状态: {}", dto.getInstanceStatus());
        log.info("节点类型: {}", dto.getNodeType());
        log.info("是否结束: {}", dto.getIsEnd());
        log.info("是否变更实例: {}", dto.getIsChangeInstance());
        log.info("系统备注: {}", dto.getSysRemark());
        log.info("业务变量: {}", dto.getVariable());
        log.info("表单数据: {}", dto.getForm());
        
        try {
            String result = repairWorkflowAppService.handleWorkflowCallback(dto);
            log.info("工作流回调处理成功，返回结果: {}", result);
            log.info("=== 工作流回调接口处理完成 ===");
            return result;
        } catch (Exception e) {
            log.error("工作流回调处理失败，实例ID: {}, 业务ID: {}, 错误信息: {}", 
                    dto.getInstanceId(), dto.getBusinessId(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 取消工作流
     *
     * @param instanceId 工作流实例ID
     * @param dto 取消请求DTO
     * @return 取消结果
     */
    @PostMapping("/{instanceId}/cancel")
    @Operation(summary = "取消工作流", description = "取消工作流")
    public CancelWorkflowResponseDTO cancelWorkflow(
            @Parameter(description = "工作流实例ID") @PathVariable String instanceId,
            @Valid @RequestBody CancelWorkflowRequestDTO dto) {
        log.info("取消工作流，实例ID: {}, 用户: {}", instanceId, dto.getUserName());
        return repairWorkflowAppService.cancelWorkflow(instanceId, dto);
    }

    /**
     * 工作流审批接口
     *
     * @param instanceStepId 实例步骤ID
     * @param dto 审批请求DTO
     * @return 审批结果
     */
    @PostMapping("/{instanceStepId}/approval")
    @Operation(summary = "工作流审批", description = "工作流审批")
    public WorkflowApprovalResponseDTO workflowApproval(
            @Parameter(description = "实例步骤ID") @PathVariable String instanceStepId,
            @Valid @RequestBody WorkflowApprovalRequestDTO dto) {
        log.info("工作流审批，实例步骤ID: {}, 操作类型: {}, 用户: {}", 
                instanceStepId, dto.getOperateType(), dto.getUserName());
        return repairWorkflowAppService.workflowApproval(instanceStepId, dto);
    }

    /**
     * 查询工作流实例详情
     *
     * @param workflowInstanceId 工作流实例ID
     * @return 工作流实例详情
     */
    @GetMapping("/{workflowInstanceId}")
    @Operation(summary = "查询工作流实例详情", description = "查询工作流实例详情")
    public InstanceDTO getWorkflowInstance(
            @Parameter(description = "工作流实例ID") @PathVariable String workflowInstanceId) {
        log.info("查询工作流实例详情，实例ID: {}", workflowInstanceId);
        return repairWorkflowAppService.getWorkflowInstance(workflowInstanceId);
    }

}

