package com.inboyu.order.app.dto.request.repair;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 客户端完成确认或取消确认请求DTO
 */
@Data
@Schema(description = "客户端完成确认或取消确认请求")
public class RepairOrderConfirmRequestDTO {

    @Schema(description = "报修工单ID", example = "123456789")
    @NotNull(message = "报修工单ID不能为空")
    private Long repairOrderId;

    @Schema(description = "确认类型", example = "confirm", allowableValues = {"confirm", "rework"})
    @NotBlank(message = "确认类型不能为空")
    private String confirmType;

    @Schema(description = "确认备注", example = "维修完成，满意")
    private String confirmComment;

    @Schema(description = "返工说明", example = "突然就需要了")
    private String reworkInstructions;
}
