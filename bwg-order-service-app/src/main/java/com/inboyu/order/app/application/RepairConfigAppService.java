package com.inboyu.order.app.application;

import com.inboyu.order.app.dto.request.repair.RepairConfigCreateRequestDTO;
import com.inboyu.order.app.dto.request.repair.RepairConfigUpdateRequestDTO;
import com.inboyu.order.app.dto.response.repair.RepairConfigDetailResponseDTO;
import com.inboyu.order.app.dto.response.repair.RepairItemResponseDTO;

import java.util.List;

/**
 * 报修配置应用服务接口
 * 定义报修配置相关的业务用例
 */
public interface RepairConfigAppService {

    /**
     * 获取系统级所有报修物品列表（无分页）
     *
     * @return 报修物品列表
     */
    List<RepairItemResponseDTO> getAllRepairItems();

    /**
     * 新增门店报修配置
     *
     * @param dto 新增请求DTO
     * @return 配置详情
     */
    RepairConfigDetailResponseDTO createRepairConfig(RepairConfigCreateRequestDTO dto);

    /**
     * 修改门店报修配置
     *
     * @param dto 修改请求DTO
     * @return 配置详情
     */
    RepairConfigDetailResponseDTO updateRepairConfig(RepairConfigUpdateRequestDTO dto);

    /**
     * 查询门店报修配置详情
     *
     * @param storeId 配置ID
     * @return 配置详情
     */
    RepairConfigDetailResponseDTO getRepairConfigDetail(String storeId);

    /**
     * 根据门店ID和物品名称模糊搜索门店可报修物品
     *
     * @param storeId 门店ID
     * @param keyword 搜索关键词
     * @return 报修物品列表
     */
    List<RepairItemResponseDTO> searchStoreRepairItems(String storeId, String keyword);
}
