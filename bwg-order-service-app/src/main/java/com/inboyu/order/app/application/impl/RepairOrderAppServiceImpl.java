package com.inboyu.order.app.application.impl;

import com.inboyu.order.app.application.RepairOrderAppService;
import com.inboyu.order.app.dto.converter.RepairOrderConverter;
import com.inboyu.order.app.dto.request.repair.BatchRepairOrderCreateRequestDTO;
import com.inboyu.order.app.dto.request.repair.CancelRepairOrderRequestDTO;
import com.inboyu.order.app.dto.request.repair.RepairOrderAcceptRejectRequestDTO;
import com.inboyu.order.app.dto.request.repair.RepairOrderCompleteRequestDTO;
import com.inboyu.order.app.dto.request.repair.RepairOrderConfirmRequestDTO;
import com.inboyu.order.app.dto.request.repair.RepairOrderProgressRequestDTO;
import com.inboyu.order.app.dto.response.repair.BatchRepairOrderCreateResponseDTO;
import com.inboyu.order.app.dto.response.repair.CancelRepairOrderResponseDTO;
import com.inboyu.order.app.dto.response.repair.RepairOrderAcceptRejectResponseDTO;
import com.inboyu.order.app.dto.response.repair.RepairOrderCompleteResponseDTO;
import com.inboyu.order.app.dto.response.repair.RepairOrderConfirmResponseDTO;
import com.inboyu.order.app.dto.response.repair.RepairOrderProgressResponseDTO;
import com.inboyu.order.app.dto.response.repair.RepairOrderResponseDTO;
import com.inboyu.order.app.dto.response.repair.*;
import com.inboyu.order.domain.repair.model.RepairOrder;
import com.inboyu.order.domain.repair.model.RepairOrderId;
import com.inboyu.order.domain.repair.model.RepairProgress;
import com.inboyu.order.domain.repair.service.RepairOrderDomainService;
import com.inboyu.order.domain.repair.service.RepairProgressDomainService;
import com.inboyu.order.domain.repair.service.WorkflowService;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 报修订单应用服务实现
 */
@Slf4j
@Service
public class RepairOrderAppServiceImpl implements RepairOrderAppService {

    @Autowired
    private RepairOrderDomainService repairOrderDomainService;

    @Autowired
    private RepairOrderConverter repairOrderConverter;

    @Autowired
    private WorkflowService workflowService;
    @Autowired
    private RepairProgressDomainService repairProgressDomainService;

    /**
     * 批量创建报修订单
     * 原子操作：要么全部成功，要么全部失败
     *
     * @param request 批量创建请求
     * @return 批量创建响应
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchRepairOrderCreateResponseDTO batchCreateRepairOrders(BatchRepairOrderCreateRequestDTO request) {
        log.info("开始批量创建报修订单，总数量：{}", request.getRepairOrders().size());

        try {
            List<RepairOrderResponseDTO> successOrders = new ArrayList<>();

            // 批量转换为领域对象
            List<RepairOrder> repairOrders = request.getRepairOrders().stream()
                    .map(repairOrderConverter::toDomain)
                    .collect(Collectors.toList());

            // 批量创建报修订单
            List<RepairOrder> createdOrders = repairOrderDomainService.batchCreateRepairOrders(repairOrders);

            // 批量转换为响应DTO
            successOrders = createdOrders.stream()
                    .map(repairOrderConverter::toResponseDTO)
                    .collect(Collectors.toList());

            log.info("批量创建报修订单成功，总数量：{}", successOrders.size());

            // 构建成功响应
            BatchRepairOrderCreateResponseDTO response = new BatchRepairOrderCreateResponseDTO();
            response.setSuccessOrders(successOrders);
            response.setSuccessCount(successOrders.size());

            return response;

        } catch (Exception e) {
            log.error("批量创建报修订单失败，错误信息：{}", e.getMessage(), e);
            // 事务会自动回滚，所有操作都会被撤销
            throw new RuntimeException("批量创建报修订单失败：" + e.getMessage(), e);
        }
    }

    /**
     * 客户端取消报修
     *
     * @param request 取消报修请求
     * @return 取消报修响应
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CancelRepairOrderResponseDTO cancelRepairOrder(CancelRepairOrderRequestDTO request) {
        log.info("开始取消报修订单，订单ID：{}，取消原因：{}", request.getRepairOrderId(), request.getCancelReason());

        try {
            // 转换为领域对象
            RepairOrderId repairOrderId = RepairOrderId.of(request.getRepairOrderId());

            // 调用领域服务取消报修订单
            RepairOrder repairOrder = repairOrderDomainService.cancelRepairOrder(repairOrderId, request.getCancelReason());

            // 调用工作流服务取消工作流
            String workflowInstanceId = repairOrder.getWorkflowId().getValue().toString();
            String cancelResult = workflowService.cancelWorkflow(
                    workflowInstanceId,
                    "客户", // 用户名
                    "user_type.user", // 用户类型
                    "1001", // 用户ID
                    request.getCancelReason()
            );

            log.info("取消报修订单成功，订单ID：{}，工作流取消结果：{}", request.getRepairOrderId(), cancelResult);

            return CancelRepairOrderResponseDTO.builder()
                    .success(true)
                    .message("报修工单取消成功")
                    .workflowInstanceId(workflowInstanceId)
                    .build();

        } catch (Exception e) {
            log.error("取消报修订单失败，订单ID：{}，错误信息：{}", request.getRepairOrderId(), e.getMessage(), e);
            throw new RuntimeException("取消报修订单失败：" + e.getMessage(), e);
        }
    }

    /**
     * 客户端获取报修进度
     *
     * @param request 获取进度请求
     * @return 报修进度响应
     */
    @Override
    public RepairOrderProgressResponseDTO getRepairOrderProgress(RepairOrderProgressRequestDTO request) {
        log.info("开始获取报修订单进度，订单ID：{}", request.getRepairOrderId());

        try {
            // 转换为领域对象
            RepairOrderId repairOrderId = RepairOrderId.of(request.getRepairOrderId());

            // 获取报修订单详情
            RepairOrder repairOrder = repairOrderDomainService.getRepairOrderDetail(repairOrderId);
            if (repairOrder == null) {
                throw new IllegalArgumentException("报修订单不存在");
            }

            // 获取进度列表
            List<RepairProgress> progressList = repairProgressDomainService.getRepairOrderProgressList(repairOrderId);

            // 获取工作流详情
            String workflowInstanceId = repairOrder.getWorkflowId().getValue().toString();
            String workflowDetail = workflowService.getWorkflowInstance(workflowInstanceId);

            // 转换进度列表为DTO
            List<RepairOrderProgressResponseDTO.ProgressItemDTO> progressItemDTOs = progressList.stream()
                    .map(this::convertToProgressItemDTO)
                    .collect(Collectors.toList());

            log.info("获取报修订单进度成功，订单ID：{}，进度数量：{}", request.getRepairOrderId(), progressItemDTOs.size());

            return RepairOrderProgressResponseDTO.builder()
                    .repairOrderId(request.getRepairOrderId())
                    .currentStatus(repairOrder.getStatus().getValue())
                    .workflowInstanceId(workflowInstanceId)
                    .workflowDetail(workflowDetail)
                    .progressList(progressItemDTOs)
                    .build();

        } catch (Exception e) {
            log.error("获取报修订单进度失败，订单ID：{}，错误信息：{}", request.getRepairOrderId(), e.getMessage(), e);
            throw new RuntimeException("获取报修订单进度失败：" + e.getMessage(), e);
        }
    }

    /**
     * 客户端完成确认或取消确认
     *
     * @param request 确认请求
     * @return 确认响应
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public RepairOrderConfirmResponseDTO confirmRepairOrder(RepairOrderConfirmRequestDTO request) {
        log.info("开始确认报修订单，订单ID：{}，确认类型：{}", request.getRepairOrderId(), request.getConfirmType());

        try {
            // 转换为领域对象
            RepairOrderId repairOrderId = RepairOrderId.of(request.getRepairOrderId());

            // 调用领域服务确认报修订单
            RepairOrder repairOrder = repairOrderDomainService.confirmRepairOrder(
                    repairOrderId,
                    request.getConfirmType(),
                    request.getConfirmComment(),
                    request.getReworkInstructions()
            );

            // 获取工作流实例ID
            String workflowInstanceId = repairOrder.getWorkflowId().getValue().toString();

            // 根据确认类型调用相应的工作流操作
            if ("confirm".equals(request.getConfirmType())) {
                // 确认完成 - 调用工作流审批通过
                String workflowResult = workflowService.workflowApproval(
                        workflowInstanceId,
                        "operate_rst_type.pass",
                        request.getConfirmComment(),
                        "user_type.user",
                        "1001", // 用户ID
                        "客户"
                );
                log.info("确认完成工作流审批结果：{}", workflowResult);
            } else if ("rework".equals(request.getConfirmType())) {
                // 请求返工 - 使用同一个工作流实例，调用工作流审批拒绝
                String workflowResult = workflowService.workflowApproval(
                        workflowInstanceId,
                        "operate_rst_type.reject",
                        request.getReworkInstructions(),
                        "user_type.user",
                        "1001", // 用户ID
                        "客户"
                );
                log.info("请求返工工作流审批结果：{}", workflowResult);
            }

            log.info("确认报修订单成功，订单ID：{}，确认类型：{}", request.getRepairOrderId(), request.getConfirmType());

            return RepairOrderConfirmResponseDTO.builder()
                    .success(true)
                    .message("确认操作成功")
                    .confirmType(request.getConfirmType())
                    .workflowInstanceId(workflowInstanceId)
                    .reworkInstructions(request.getReworkInstructions())
                    .build();

        } catch (Exception e) {
            log.error("确认报修订单失败，订单ID：{}，错误信息：{}", request.getRepairOrderId(), e.getMessage(), e);
            throw new RuntimeException("确认报修订单失败：" + e.getMessage(), e);
        }
    }

    /**
     * 运营端受理/不受理报修
     *
     * @param request 受理/不受理请求
     * @return 受理/不受理响应
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public RepairOrderAcceptRejectResponseDTO acceptRejectRepairOrder(RepairOrderAcceptRejectRequestDTO request) {
        log.info("开始受理/不受理报修订单，订单ID：{}，操作类型：{}", request.getRepairOrderId(), request.getOperationType());

        try {
            // 业务规则验证
            validateAcceptRejectRequest(request);

            // 转换为领域对象
            RepairOrderId repairOrderId = RepairOrderId.of(request.getRepairOrderId());

            // 获取报修订单详情
            RepairOrder repairOrder = repairOrderDomainService.getRepairOrderDetail(repairOrderId);
            if (repairOrder == null) {
                throw new IllegalArgumentException("报修订单不存在");
            }

            // 获取工作流实例ID
            String workflowInstanceId = repairOrder.getWorkflowId().getValue().toString();

            // 根据操作类型调用相应的领域服务
            RepairOrder updatedOrder;
            String workflowOperateType;

            if ("accept".equals(request.getOperationType())) {
                // 受理操作
                updatedOrder = repairOrderDomainService.acceptRepairOrder(
                        repairOrderId,
                        request.getOperatorId(),
                        request.getOperationComment()
                );
                workflowOperateType = "operate_rst_type.pass";
            } else if ("reject".equals(request.getOperationType())) {
                // 不受理操作
                updatedOrder = repairOrderDomainService.rejectRepairOrder(
                        repairOrderId,
                        request.getOperatorId(),
                        request.getOperationComment()
                );
                workflowOperateType = "operate_rst_type.reject";
            } else {
                throw new IllegalArgumentException("不支持的操作类型：" + request.getOperationType());
            }

            // 调用工作流审批服务
            String currentStepId = getCurrentStepIdByWorkflowInstanceId(workflowInstanceId);
            String workflowResult = workflowService.workflowApproval(
                currentStepId,
                workflowOperateType,
                request.getOperationComment(),
                request.getOperatorType(),
                request.getOperatorId().toString(),
                request.getOperatorName()
            );

            // 创建进度记录
            repairProgressDomainService.createAcceptRejectProgress(updatedOrder, request.getOperationType(), request.getOperationComment());

            log.info("受理/不受理报修订单成功，订单ID：{}，操作类型：{}", request.getRepairOrderId(), request.getOperationType());

            return RepairOrderAcceptRejectResponseDTO.builder()
                    .success(true)
                    .message("操作成功")
                    .operationType(request.getOperationType())
                    .workflowInstanceId(workflowInstanceId)
                    .workflowStepId(currentStepId)
                    .build();

        } catch (Exception e) {
            log.error("受理/不受理报修订单失败，订单ID：{}，错误信息：{}", request.getRepairOrderId(), e.getMessage(), e);
            throw new RuntimeException("受理/不受理报修订单失败：" + e.getMessage(), e);
        }
    }

    /**
     * 运营端完成维修
     *
     * @param request 完成维修请求
     * @return 完成维修响应
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public RepairOrderCompleteResponseDTO completeRepairOrder(RepairOrderCompleteRequestDTO request) {
        log.info("开始完成维修，订单ID：{}，维修人员：{}", request.getRepairOrderId(), request.getMaintainerName());

        try {
            // 业务规则验证
            validateCompleteRequest(request);

            // 转换为领域对象
            RepairOrderId repairOrderId = RepairOrderId.of(request.getRepairOrderId());

            // 获取报修订单详情
            RepairOrder repairOrder = repairOrderDomainService.getRepairOrderDetail(repairOrderId);
            if (repairOrder == null) {
                throw new IllegalArgumentException("报修订单不存在");
            }

            // 调用领域服务完成维修
            RepairOrder updatedOrder = repairOrderDomainService.completeRepairOrder(
                    repairOrderId,
                    request.getMaintainerId(),
                    request.getCompleteComment(),
                    request.getActualCompleteTime()
            );

            // 获取工作流实例ID
            String workflowInstanceId = repairOrder.getWorkflowId().getValue().toString();

            // 调用工作流审批服务 - 完成维修通常需要审批通过
            String currentStepId = getCurrentStepIdByWorkflowInstanceId(workflowInstanceId);
            String workflowResult = workflowService.workflowApproval(
                currentStepId,
                "operate_rst_type.pass",
                request.getCompleteComment(),
                request.getMaintainerType(),
                request.getMaintainerId().toString(),
                request.getMaintainerName()
            );

            // 创建进度记录
            repairProgressDomainService.createCompleteProgress(updatedOrder, request.getMaintainerName(), request.getCompleteComment());

            log.info("完成维修成功，订单ID：{}，维修人员：{}", request.getRepairOrderId(), request.getMaintainerName());

            return RepairOrderCompleteResponseDTO.builder()
                    .success(true)
                    .message("维修完成")
                    .workflowInstanceId(workflowInstanceId)
                    .workflowStepId(currentStepId)
                    .actualCompleteTime(request.getActualCompleteTime())
                    .build();

        } catch (Exception e) {
            log.error("完成维修失败，订单ID：{}，错误信息：{}", request.getRepairOrderId(), e.getMessage(), e);
            throw new RuntimeException("完成维修失败：" + e.getMessage(), e);
        }
    }

    /**
     * 转换进度对象为DTO
     *
     * @param progress 进度对象
     * @return 进度DTO
     */
    private RepairOrderProgressResponseDTO.ProgressItemDTO convertToProgressItemDTO(RepairProgress progress) {
        return RepairOrderProgressResponseDTO.ProgressItemDTO.builder()
                .progressId(progress.getProgressId().getValue())
                .operatorName(progress.getOperatorName())
                .detail(progress.getDetail())
                .subDetail(progress.getSubDetail())
                .operatorType(progress.getOperatorType().getValue())
                .status(progress.getStatus().getValue())
                .comment(progress.getComment())
                .createTime(progress.getCreateTime())
                .build();
    }

    /**
     * 根据工作流实例ID获取当前步骤ID
     * 这是一个简化的实现，实际项目中可能需要调用工作流服务API
     *
     * @param workflowInstanceId 工作流实例ID
     * @return 当前步骤ID
     */
    private String getCurrentStepIdByWorkflowInstanceId(String workflowInstanceId) {
        // TODO: 实际实现中应该调用工作流服务API获取当前步骤ID
        // 这里使用一个简化的实现，返回一个基于实例ID的步骤ID
        // 实际项目中可能需要：
        // 1. 调用工作流服务API获取实例详情
        // 2. 解析JSON响应获取当前步骤ID
        // 3. 处理异常情况

        log.info("获取工作流实例当前步骤ID，实例ID：{}", workflowInstanceId);

        // 简化实现：返回一个模拟的步骤ID
        // 实际项目中应该调用类似这样的API：
        // String workflowDetail = workflowService.getWorkflowInstance(workflowInstanceId);
        // 然后解析JSON获取当前步骤ID

        return workflowInstanceId + "_step_001";
    }

    /**
     * 验证受理/不受理请求
     *
     * @param request 受理/不受理请求
     */
    private void validateAcceptRejectRequest(RepairOrderAcceptRejectRequestDTO request) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }

        // 验证操作类型
        if (!"accept".equals(request.getOperationType()) && !"reject".equals(request.getOperationType())) {
            throw new IllegalArgumentException("操作类型只能是accept或reject");
        }

        // 如果是不受理操作，验证拒绝理由
        if ("reject".equals(request.getOperationType())) {
            if (request.getOperationComment() == null || request.getOperationComment().trim().isEmpty()) {
                throw new IllegalArgumentException("不受理操作必须提供拒绝理由");
            }
            if (request.getOperationComment().length() > 50) {
                throw new IllegalArgumentException("拒绝理由不能超过50个字符");
            }
        }

        // 验证操作人信息
        if (request.getOperatorId() == null || request.getOperatorId() <= 0) {
            throw new IllegalArgumentException("操作人ID必须大于0");
        }
        if (request.getOperatorName() == null || request.getOperatorName().trim().isEmpty()) {
            throw new IllegalArgumentException("操作人姓名不能为空");
        }
    }

    /**
     * 验证完成维修请求
     *
     * @param request 完成维修请求
     */
    private void validateCompleteRequest(RepairOrderCompleteRequestDTO request) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }

        // 验证完成说明
        if (request.getCompleteComment() == null || request.getCompleteComment().trim().isEmpty()) {
            throw new IllegalArgumentException("完成说明不能为空");
        }
        if (request.getCompleteComment().length() > 50) {
            throw new IllegalArgumentException("完成说明不能超过50个字符");
        }

        // 验证维修人员信息
        if (request.getMaintainerId() == null || request.getMaintainerId() <= 0) {
            throw new IllegalArgumentException("维修人员ID必须大于0");
        }
        if (request.getMaintainerName() == null || request.getMaintainerName().trim().isEmpty()) {
            throw new IllegalArgumentException("维修人员姓名不能为空");
        }

        // 验证实际完成时间格式（如果提供）
        if (request.getActualCompleteTime() != null && !request.getActualCompleteTime().trim().isEmpty()) {
            try {
                java.time.LocalDateTime.parse(request.getActualCompleteTime(),
                    java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } catch (Exception e) {
                throw new IllegalArgumentException("实际完成时间格式不正确，请使用yyyy-MM-dd HH:mm:ss格式");
            }
        }
    }

    @Override
    public Pagination<RepairOrderPageListResponseDTO> pageRepairOrder(
            Integer pageNum, Integer pageSize,
            String customerId, String storeId, String status,
            String responsibleId, String roomNum, String roomId
    ) {
        log.info("分页查询报修订单列表，请求参数：pageNum={}, pageSize={}, customerId={}, storeId={}, status={}, responsibleId={}, roomNum={}, roomId={}",
                pageNum, pageSize, customerId, storeId, status, responsibleId, roomNum, roomId);

        try {
            // 调用领域服务分页查询
            Pagination<RepairOrder> repairOrderPage = repairOrderDomainService.pageRepairOrder(
                    pageNum, pageSize, customerId, storeId, status, responsibleId, roomNum, roomId
            );

            // 转换为响应DTO
            List<RepairOrderPageListResponseDTO> dtoList = repairOrderPage.getList().stream()
                    .map(repairOrderConverter::toPageListResponseDTO)
                    .collect(Collectors.toList());

            log.info("分页查询报修订单列表成功，总记录数：{}，当前页数据：{}",
                    repairOrderPage.getTotal(), dtoList.size());

            return new Pagination<>(
                    repairOrderPage.getPageNum(),
                    repairOrderPage.getPageSize(),
                    repairOrderPage.getTotalPages(),
                    repairOrderPage.getTotal(),
                    dtoList
            );

        } catch (Exception e) {
            log.error("分页查询报修订单列表失败，错误信息：{}", e.getMessage(), e);
            throw new RuntimeException("查询报修订单列表失败：" + e.getMessage(), e);
        }
    }

    public RepairOrderDetailResponseDTO getRepairOrderDetail(String orderId) {
        log.info("获取报修订单详情，订单ID：{}", orderId);

        try {
            RepairOrderId repairOrderId = RepairOrderId.of(orderId);

            RepairOrder repairOrder = repairOrderDomainService.getRepairOrderDetail(repairOrderId);
            if (repairOrder == null) {
                throw new IllegalArgumentException("报修订单不存在");
            }

            // 转换为响应DTO
            RepairOrderDetailResponseDTO responseDTO = repairOrderConverter.toDetailResponseDTO(repairOrder);

            log.info("获取报修订单详情成功，订单ID：{}", orderId);

            return responseDTO;

        } catch (Exception e) {
            log.error("获取报修订单详情失败，订单ID：{}，错误信息：{}", orderId, e.getMessage(), e);
            throw new RuntimeException("获取报修订单详情失败：" + e.getMessage(), e);
        }
    }
}
