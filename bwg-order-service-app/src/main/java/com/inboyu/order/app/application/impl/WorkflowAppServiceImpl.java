package com.inboyu.order.app.application.impl;

import com.inboyu.admin.dto.request.workflow.*;
import com.inboyu.admin.dto.response.workflow.*;
import com.inboyu.order.api.WorkflowFeignClient;
import com.inboyu.order.app.application.WorkflowAppService;
import com.inboyu.spring.cloud.starter.http.api.container.FeignAccessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 工作流应用服务实现类
 *
 */
@Slf4j
@Service
public class WorkflowAppServiceImpl implements WorkflowAppService {

    @Autowired
    private FeignAccessor feignAccessor;

    @Override
    public InstanceCreateResponseDTO createWorkflow(InstanceCreateRequestDTO request) {
        log.info("开始创建工作流实例，业务ID: {}, 业务类型: {}", 
                request.getBusinessId(), request.getBusinessType());
        
        try {
            // 获取 Feign 客户端
            WorkflowFeignClient workflowFeignClient = feignAccessor.get(WorkflowFeignClient.class);
            
            // 调用远程服务创建工作流
            InstanceCreateResponseDTO response = workflowFeignClient.create(request);
            
            log.info("工作流实例创建成功，实例ID: {}", response.getWorkflowInstanceId());
            return response;
            
        } catch (Exception e) {
            log.error("创建工作流实例失败，业务ID: {}, 错误信息: {}", 
                    request.getBusinessId(), e.getMessage(), e);
            throw new RuntimeException("创建工作流实例失败: " + e.getMessage(), e);
        }
    }

    @Override
    public InstanceStepBasicDTO approveWorkflow(String instanceStepId, InstanceApprovalRequestDTO request) {
        log.info("开始审批工作流，实例步骤ID: {}, 操作类型: {}", 
                instanceStepId, request.getOperateType());
        
        try {
            // 获取 Feign 客户端
            WorkflowFeignClient workflowFeignClient = feignAccessor.get(WorkflowFeignClient.class);
            
            // 调用远程服务审批工作流
            InstanceStepBasicDTO response = workflowFeignClient.approval(instanceStepId, request);
            
            log.info("工作流审批成功，实例步骤ID: {}", instanceStepId);
            return response;
            
        } catch (Exception e) {
            log.error("审批工作流失败，实例步骤ID: {}, 错误信息: {}", 
                    instanceStepId, e.getMessage(), e);
            throw new RuntimeException("审批工作流失败: " + e.getMessage(), e);
        }
    }

    @Override
    public InstanceCancelResponseDTO cancelWorkflow(String instanceId, InstanceCancelRequestDTO request) {
        log.info("开始撤销工作流，实例ID: {}", instanceId);
        
        try {
            // 获取 Feign 客户端
            WorkflowFeignClient workflowFeignClient = feignAccessor.get(WorkflowFeignClient.class);
            
            // 调用远程服务撤销工作流
            InstanceCancelResponseDTO response = workflowFeignClient.cancel(instanceId, request);
            
            log.info("工作流撤销成功，实例ID: {}", instanceId);
            return response;
            
        } catch (Exception e) {
            log.error("撤销工作流失败，实例ID: {}, 错误信息: {}", 
                    instanceId, e.getMessage(), e);
            throw new RuntimeException("撤销工作流失败: " + e.getMessage(), e);
        }
    }

    @Override
    public InstanceDTO getWorkflowInstance(String workflowInstanceId) {
        log.info("开始查询工作流实例，实例ID: {}", workflowInstanceId);
        
        try {
            // 获取 Feign 客户端
            WorkflowFeignClient workflowFeignClient = feignAccessor.get(WorkflowFeignClient.class);
            
            // 调用远程服务查询工作流实例
            InstanceDTO response = workflowFeignClient.getWorkflowInstance(workflowInstanceId);
            
            log.info("工作流实例查询成功，实例ID: {}", workflowInstanceId);
            return response;
            
        } catch (Exception e) {
            log.error("查询工作流实例失败，实例ID: {}, 错误信息: {}", 
                    workflowInstanceId, e.getMessage(), e);
            throw new RuntimeException("查询工作流实例失败: " + e.getMessage(), e);
        }
    }
}