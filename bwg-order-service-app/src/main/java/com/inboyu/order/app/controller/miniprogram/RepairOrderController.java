package com.inboyu.order.app.controller.miniprogram;

import com.inboyu.order.app.application.RepairOrderAppService;
import com.inboyu.order.app.dto.request.repair.BatchRepairOrderCreateRequestDTO;
import com.inboyu.order.app.dto.request.repair.CancelRepairOrderRequestDTO;
import com.inboyu.order.app.dto.request.repair.RepairOrderConfirmRequestDTO;
import com.inboyu.order.app.dto.request.repair.RepairOrderProgressRequestDTO;
import com.inboyu.order.app.dto.response.repair.*;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController("miniprogramRepairOrderController")
@RequestMapping("/api/v1/miniprogram/repair-order")
@Tag(name = "报修订单-用户端小程序", description = "用户端小程序-报修订单接口")
@Validated
public class RepairOrderController {

    @Autowired
    private RepairOrderAppService repairOrderAppService;

    /**
     * 小程序-报修订单详情
     */
    @GetMapping("/{repairOrderId}")
    @Operation(summary = "用户端小程序-报修单详情", description = "小程序-报修单详情")
    public char getOrderDetail() {
        log.info("小程序-报修单详情");
        return '2';
    }

    /**
     * 小程序-报修订单分页列表
     */
    @GetMapping
    @Operation(summary = "小程序-报修订单分页列表", description = "小程序-报修单详情")
    public Pagination<RepairOrderPageListResponseDTO> pageRepairOrder(
            @RequestParam @Schema(description = "当前页") Integer pageNum,
            @RequestParam @Schema(description = "每页条数") Integer pageSize,
            @RequestParam(required = false) @Schema(description = "客户ID") String customerId,
            @RequestParam(required = false) @Schema(description = "门店ID") String storeId,
            @RequestParam(required = false) @Schema(description = "报修状态") String status,
            @RequestParam(required = false) @Schema(description = "负责人id") String responsibleId,
            @RequestParam(required = false) @Schema(description = "房间号") String roomNum,
            @RequestParam(required = false) @Schema(description = "房间ID") String roomId) {
        return repairOrderAppService.pageRepairOrder(pageNum, pageSize, customerId, storeId, status, responsibleId, roomNum, roomId);
    }

    /**
     * 批量创建报修订单
     */
    @Operation(summary = "用户端小程序-批量创建报修订单", description = "支持一次提交多个报修订单，每个订单可包含多个附件")
    @PostMapping("/batch")
    public BatchRepairOrderCreateResponseDTO batchCreateRepairOrders(
            @Valid @RequestBody BatchRepairOrderCreateRequestDTO request) {
        return repairOrderAppService.batchCreateRepairOrders(request);
    }

    /**
     * 客户端取消报修
     */
    @Operation(summary = "用户端小程序-取消报修", description = "客户端取消报修工单，调用工作流取消接口")
    @PostMapping("/cancel")
    public CancelRepairOrderResponseDTO cancelRepairOrder(
            @Valid @RequestBody CancelRepairOrderRequestDTO request) {
        log.info("客户端取消报修，订单ID：{}", request.getRepairOrderId());
        return repairOrderAppService.cancelRepairOrder(request);
    }

    /**
     * 客户端获取报修进度
     */
    @Operation(summary = "用户端小程序-获取报修进度", description = "客户端获取报修工单的详细进度信息")
    @PostMapping("/progress")
    public RepairOrderProgressResponseDTO getRepairOrderProgress(
            @Valid @RequestBody RepairOrderProgressRequestDTO request) {
        log.info("客户端获取报修进度，订单ID：{}", request.getRepairOrderId());
        return repairOrderAppService.getRepairOrderProgress(request);
    }

    /**
     * 客户端完成确认或取消确认
     */
    @Operation(summary = "用户端小程序-完成确认或取消确认", description = "客户端对报修工单进行完成确认或取消确认操作")
    @PostMapping("/confirm")
    public RepairOrderConfirmResponseDTO confirmRepairOrder(
            @Valid @RequestBody RepairOrderConfirmRequestDTO request) {
        log.info("客户端确认报修订单，订单ID：{}，确认类型：{}", request.getRepairOrderId(), request.getConfirmType());
        return repairOrderAppService.confirmRepairOrder(request);
    }

}
