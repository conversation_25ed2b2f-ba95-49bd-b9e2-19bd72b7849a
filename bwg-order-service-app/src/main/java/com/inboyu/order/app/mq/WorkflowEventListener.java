package com.inboyu.order.app.mq;

import com.alibaba.fastjson2.JSON;
import com.inboyu.admin.constant.AdminRocketMqTopicConstant;
import com.inboyu.admin.event.workflow.WorkflowApprovalEvent;
import com.inboyu.admin.event.workflow.WorkflowCompleteEvent;
import com.inboyu.admin.event.workflow.WorkflowCreateEvent;
import com.inboyu.alimq.annotation.RocketMQTag;
import com.inboyu.alimq.annotation.RocketMQTopic;
import com.inboyu.order.domain.repair.service.RepairLogDomainService;
import com.inboyu.order.domain.repair.service.WorkflowEventDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 工作流事件监听器
 * 负责接收工作流事件并委托给领域服务处理
 * 遵循DDD架构，不包含业务逻辑
 */
@Slf4j
@Component
@RocketMQTopic(topic = AdminRocketMqTopicConstant.TOPIC_BWG_TENANT)
public class WorkflowEventListener {

    @Autowired
    private RepairLogDomainService repairLogDomainService;
    
    @Autowired
    private WorkflowEventDomainService workflowEventDomainService;

    /**
     * 工作流创建事件处理
     * 当报修工单的工作流被创建时触发
     * 
     * @param event 工作流创建事件
     */
    @RocketMQTag(tag = AdminRocketMqTopicConstant.TAG_WORKFLOW_CREATE)
    public void workflowCreate(WorkflowCreateEvent event) {
        try {
            log.info("收到工作流创建事件: {}", JSON.toJSONString(event));
            
            // 记录日志
            repairLogDomainService.save(AdminRocketMqTopicConstant.TAG_WORKFLOW_CREATE, JSON.toJSONString(event));
            
            // 委托给领域服务处理业务逻辑
            workflowEventDomainService.handleWorkflowCreateEvent(
                event.getInstance().getBusinessId(), 
                event.getInstance().getBusinessType()
            );
                
        } catch (Exception e) {
            log.error("处理工作流创建事件失败", e);
        }
    }

    /**
     * 工作流审批事件处理
     * 当工作流审批节点被处理时触发（受理、不受理、转交、挂起等）
     * 
     * @param event 工作流审批事件
     */
    @RocketMQTag(tag = AdminRocketMqTopicConstant.TAG_WORKFLOW_APPROVAL)
    public void workflowApproval(WorkflowApprovalEvent event) {
        try {
            log.info("收到工作流审批事件: {}", JSON.toJSONString(event));
            
            // 记录日志
            repairLogDomainService.save(AdminRocketMqTopicConstant.TAG_WORKFLOW_APPROVAL, JSON.toJSONString(event));
            
            // 委托给领域服务处理业务逻辑
            workflowEventDomainService.handleWorkflowApprovalEvent(
                event.getInstance().getBusinessId(),
                event.getInstance().getBusinessType(),
                event.getOperateType(),
                event.getUser() != null ? event.getUser().getUserId() : 0L,
                event.getUser() != null ? event.getUser().getUserName() : "系统",
                event.getComment() != null ? event.getComment().toString() : "",
                event.getInstance().getInstanceStatus().name(),
                event.getInstance().getVariables()
            );
                
        } catch (Exception e) {
            log.error("处理工作流审批事件失败", e);
        }
    }

    /**
     * 工作流完成事件处理
     * 当工作流完成时触发
     * 
     * @param event 工作流完成事件
     */
    @RocketMQTag(tag = AdminRocketMqTopicConstant.TAG_WORKFLOW_COMPLETE)
    public void workflowComplete(WorkflowCompleteEvent event) {
        try {
            log.info("收到工作流完成事件: {}", JSON.toJSONString(event));
            
            // 记录日志
            repairLogDomainService.save(AdminRocketMqTopicConstant.TAG_WORKFLOW_COMPLETE, JSON.toJSONString(event));
            
            // 委托给领域服务处理业务逻辑
            workflowEventDomainService.handleWorkflowCompleteEvent(
                event.getInstance().getBusinessId(),
                event.getInstance().getBusinessType(),
                event.getUser() != null ? event.getUser().getUserId() : 0L,
                event.getUser() != null ? event.getUser().getUserName() : "系统"
            );
                
        } catch (Exception e) {
            log.error("处理工作流完成事件失败", e);
        }
    }
}
