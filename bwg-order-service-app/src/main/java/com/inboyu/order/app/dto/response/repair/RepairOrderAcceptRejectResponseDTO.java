package com.inboyu.order.app.dto.response.repair;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * 运营端受理/不受理报修响应DTO
 */
@Data
@Builder
@Schema(description = "运营端受理/不受理报修响应")
public class RepairOrderAcceptRejectResponseDTO {

    @Schema(description = "操作是否成功")
    private Boolean success;

    @Schema(description = "响应消息")
    private String message;

    @Schema(description = "操作类型")
    private String operationType;

    @Schema(description = "工作流实例ID")
    private String workflowInstanceId;

    @Schema(description = "工作流步骤ID")
    private String workflowStepId;
}
