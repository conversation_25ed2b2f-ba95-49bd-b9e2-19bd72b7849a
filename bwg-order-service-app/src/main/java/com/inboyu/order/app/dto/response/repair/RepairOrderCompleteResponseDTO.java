package com.inboyu.order.app.dto.response.repair;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * 运营端完成维修响应DTO
 */
@Data
@Builder
@Schema(description = "运营端完成维修响应")
public class RepairOrderCompleteResponseDTO {

    @Schema(description = "操作是否成功")
    private Boolean success;

    @Schema(description = "响应消息")
    private String message;

    @Schema(description = "工作流实例ID")
    private String workflowInstanceId;

    @Schema(description = "工作流步骤ID")
    private String workflowStepId;

    @Schema(description = "实际完成时间")
    private String actualCompleteTime;
}
