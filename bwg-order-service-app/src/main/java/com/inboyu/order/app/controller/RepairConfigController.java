package com.inboyu.order.app.controller;

import com.inboyu.order.app.application.RepairConfigAppService;
import com.inboyu.order.app.dto.request.repair.RepairConfigCreateRequestDTO;
import com.inboyu.order.app.dto.request.repair.RepairConfigUpdateRequestDTO;
import com.inboyu.order.app.dto.response.repair.RepairConfigDetailResponseDTO;
import com.inboyu.order.app.dto.response.repair.RepairItemResponseDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import java.util.List;

/**
 * 门店报修配置控制器
 * 提供门店报修配置相关的REST API接口
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/repair-config")
@Tag(name = "门店报修配置管理", description = "门店报修配置相关接口")
@Validated
public class RepairConfigController {

    @Autowired
    private RepairConfigAppService repairConfigAppService;

    /**
     * 获取系统级所有报修物品列表
     *
     * @return 报修物品列表
     */
    @GetMapping("/repair-items")
    @Operation(summary = "获取系统所有报修物品列表", description = "获取系统所有报修物品列表")
    public List<RepairItemResponseDTO> getAllRepairItems() {
        log.info("获取系统所有报修物品列表");
        return repairConfigAppService.getAllRepairItems();
    }

    /**
     * 新增门店报修配置
     *
     * @param dto 新增请求DTO
     * @return 配置详情
     */
    @PostMapping
    @Operation(summary = "新增门店报修配置", description = "新增门店报修配置，同时配置门店可报修物品")
    public RepairConfigDetailResponseDTO createRepairConfig(
            @Valid @RequestBody RepairConfigCreateRequestDTO dto) {
        log.info("新增门店报修配置，门店ID: {}", dto.getStoreId());
        return repairConfigAppService.createRepairConfig(dto);
    }

    /**
     * 修改门店报修配置
     *
     * @param dto 修改请求DTO
     * @return 配置详情
     */
    @PostMapping("/update")
    @Operation(summary = "修改门店报修配置", description = "修改门店报修配置，同时修改门店可报修物品配置")
    public RepairConfigDetailResponseDTO updateRepairConfig(
            @Valid @RequestBody RepairConfigUpdateRequestDTO dto) {
        log.info("修改门店报修配置，配置ID: {}", dto.getConfigId());
        return repairConfigAppService.updateRepairConfig(dto);
    }

    /**
     * 查询门店报修配置详情
     *
     * @param storeId 门店ID
     * @return 配置详情
     */
    @GetMapping("/{storeId}")
    @Operation(summary = "查询门店报修配置详情", description = "根据门店ID查询门店报修配置详情，包含门店可报修物品")
    public RepairConfigDetailResponseDTO getRepairConfigDetail(
            @Parameter(description = "门店ID", required = true)
            @PathVariable @NotBlank(message = "配置ID不能为空") String storeId) {
        log.info("查询门店报修配置详情，门店ID: {}", storeId);
        return repairConfigAppService.getRepairConfigDetail(storeId);
    }
}
