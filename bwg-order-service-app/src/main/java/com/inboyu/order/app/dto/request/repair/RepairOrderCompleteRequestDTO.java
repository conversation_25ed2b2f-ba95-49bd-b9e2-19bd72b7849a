package com.inboyu.order.app.dto.request.repair;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 运营端完成维修请求DTO
 */
@Data
@Schema(description = "运营端完成维修请求")
public class RepairOrderCompleteRequestDTO {

    @Schema(description = "报修工单ID", example = "123456789")
    @NotNull(message = "报修工单ID不能为空")
    private Long repairOrderId;

    @Schema(description = "完成说明", example = "维修已完成，设备正常运行")
    @NotBlank(message = "完成说明不能为空")
    private String completeComment;

    @Schema(description = "维修人员ID", example = "1001")
    @NotNull(message = "维修人员ID不能为空")
    private Long maintainerId;

    @Schema(description = "维修人员姓名", example = "李师傅")
    @NotBlank(message = "维修人员姓名不能为空")
    private String maintainerName;

    @Schema(description = "维修人员类型", example = "user_type.maintainer")
    @NotBlank(message = "维修人员类型不能为空")
    private String maintainerType;

    @Schema(description = "实际完成时间", example = "2024-01-15 14:30:00")
    private String actualCompleteTime;

    @Schema(description = "维修照片URL列表", example = "[\"http://example.com/photo1.jpg\", \"http://example.com/photo2.jpg\"]")
    private java.util.List<String> repairPhotos;

    @Schema(description = "维修视频URL列表", example = "[\"http://example.com/video1.mp4\"]")
    private java.util.List<String> repairVideos;
}
