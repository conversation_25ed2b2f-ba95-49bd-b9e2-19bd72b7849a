package com.inboyu.order.app.dto.response.repair;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 报修订单响应DTO
 */
@Data
@Schema(description = "报修订单响应")
public class RepairOrderResponseDTO {

    @Schema(description = "报修订单ID", example = "200001")
    private String repairOrderId;

    @Schema(description = "客户ID", example = "1001")
    private String customerId;

    @Schema(description = "门店ID", example = "2001")
    private String storeId;

    @Schema(description = "房间ID", example = "3001")
    private String roomId;

    @Schema(description = "楼栋ID", example = "4001")
    private String buildingId;

    @Schema(description = "报修物品ID", example = "5001")
    private String itemId;

    @Schema(description = "报修物品故障描述标签", example = "水龙头漏水")
    private String itemTag;

    @Schema(description = "工作流ID", example = "6001")
    private String workflowId;

    @Schema(description = "房号", example = "A-1001")
    private String roomNumber;

    @Schema(description = "报修区域", example = "卫生间")
    private String area;

    @Schema(description = "问题描述", example = "卫生间水龙头持续滴水，影响正常使用")
    private String detail;

    @Schema(description = "联系电话", example = "13800138000")
    private String contactPhone;

    @Schema(description = "报修人姓名", example = "张三")
    private String submitter;

    @Schema(description = "意向维修日期", example = "2025-09-20")
    private LocalDate willRepairDate;

    @Schema(description = "意向维修开始时间段", example = "9")
    private LocalTime willRepairStart;

    @Schema(description = "意向维修结束时间段", example = "17")
    private LocalTime willRepairEnd;

    @Schema(description = "是否无人可直接入户，0-否 1-是", example = "0")
    private Integer isEnter;

    @Schema(description = "报修状态", example = "repair_order_status.submitted")
    private String status;

    @Schema(description = "工单类型", example = "repair_order_type.resident")
    private String orderType;

    @Schema(description = "标签状态", example = "repair_tag_status.normal")
    private String tagStatus;

    @Schema(description = "备注", example = "请在工作时间联系")
    private String remark;

    @Schema(description = "创建时间", example = "2025-09-17T10:30:00")
    private LocalDateTime createTime;

    @Schema(description = "报修附件列表")
    private List<RepairAttachmentResponseDTO> attachments;
}
