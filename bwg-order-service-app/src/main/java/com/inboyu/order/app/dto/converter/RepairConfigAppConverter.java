package com.inboyu.order.app.dto.converter;

import com.inboyu.order.app.dto.request.repair.RepairConfigCreateRequestDTO;
import com.inboyu.order.app.dto.request.repair.RepairConfigUpdateRequestDTO;
import com.inboyu.order.app.dto.request.repair.RepairTodayEnableDTO;
import com.inboyu.order.app.dto.response.repair.RepairConfigDetailResponseDTO;
import com.inboyu.order.app.dto.response.repair.RepairItemResponseDTO;
import com.inboyu.order.domain.repair.model.*;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 报修配置应用层转换器
 * 负责DTO与领域对象之间的转换
 */
@Component
public class RepairConfigAppConverter {

    /**
     * 转换为报修配置领域对象（新增）
     *
     * @param configId 配置ID
     * @param dto      请求DTO
     * @return 报修配置领域对象
     */
    public RepairConfig toRepairConfig(RepairConfigId configId, RepairConfigCreateRequestDTO dto) {
        List<RepairItemId> repairItemIds = dto.getRepairItemIds().stream()
                .map(id -> RepairItemId.of(Long.valueOf(id)))
                .collect(Collectors.toList());

        return RepairConfig.create(
                configId,
                StoreId.of(Long.valueOf(dto.getStoreId())),
                dto.getRepairStartTime(),
                dto.getRepairEndTime(),
                dto.getRepairTimeInterval(),
                dto.getRepairWeekDay(),
                dto.getMaxRepairDays(),
                RepairTodayEnable.create(dto.getRepairTodayEnable().getCode(), dto.getRepairTodayEnable().getTitle()),
                repairItemIds
        );
    }

    /**
     * 转换为报修配置领域对象（修改）
     *
     * @param dto 请求DTO
     * @return 报修配置领域对象
     */
    public RepairConfig toRepairConfig(RepairConfigUpdateRequestDTO dto) {
        List<RepairItemId> repairItemIds = dto.getRepairItemIds().stream()
                .map(id -> RepairItemId.of(Long.valueOf(id)))
                .collect(Collectors.toList());

        return RepairConfig.create(
                RepairConfigId.of(Long.valueOf(dto.getConfigId())),
                StoreId.of(Long.valueOf(dto.getStoreId())),
                dto.getRepairStartTime(),
                dto.getRepairEndTime(),
                dto.getRepairTimeInterval(),
                dto.getRepairWeekDay(),
                dto.getMaxRepairDays(),
                RepairTodayEnable.create(dto.getRepairTodayEnable().getCode(), dto.getRepairTodayEnable().getTitle()),
                repairItemIds
        );
    }

    /**
     * 转换为报修配置详情响应DTO
     *
     * @param repairConfig 报修配置领域对象
     * @param repairItems  报修物品列表
     * @return 报修配置详情响应DTO
     */
    public RepairConfigDetailResponseDTO toDetailResponseDTO(RepairConfig repairConfig, List<RepairItem> repairItems) {
        RepairConfigDetailResponseDTO dto = new RepairConfigDetailResponseDTO();
        dto.setConfigId(repairConfig.getRepairConfigId().getValue().toString());
        dto.setStoreId(repairConfig.getStoreId().getValue().toString());
        dto.setRepairStartTime(repairConfig.getRepairStartTime());
        dto.setRepairEndTime(repairConfig.getRepairEndTime());
        dto.setRepairTimeInterval(repairConfig.getRepairTimeInterval());
        dto.setRepairWeekDay(repairConfig.getRepairWeekDay());
        dto.setMaxRepairDays(repairConfig.getMaxRepairDays());

        // 转换当天报修开放状态
        RepairTodayEnableDTO todayEnableDTO = new RepairTodayEnableDTO();
        todayEnableDTO.setCode(repairConfig.getRepairTodayEnable().getCode());
        todayEnableDTO.setTitle(repairConfig.getRepairTodayEnable().getTitle());
        dto.setRepairTodayEnable(todayEnableDTO);

        // 转换报修物品列表
        List<RepairItemResponseDTO> itemDTOs = repairItems.stream()
                .map(this::toRepairItemResponseDTO)
                .collect(Collectors.toList());
        dto.setRepairItems(itemDTOs);

        return dto;
    }

    /**
     * 转换为报修物品响应DTO
     *
     * @param repairItem 报修物品领域对象
     * @return 报修物品响应DTO
     */
    public RepairItemResponseDTO toRepairItemResponseDTO(RepairItem repairItem) {
        RepairItemResponseDTO dto = new RepairItemResponseDTO();
        dto.setItemId(repairItem.getId().getValue().toString());
        dto.setKind(repairItem.getKind());
        dto.setName(repairItem.getName());
        dto.setIcon(repairItem.getIcon());
        dto.setUrl(repairItem.getUrl());
        dto.setRemark(repairItem.getRemark());
        dto.setSortOrder(repairItem.getSortOrder());
        dto.setIsActive(repairItem.getIsActive());
        return dto;
    }

    /**
     * 转换为报修物品响应DTO列表
     *
     * @param repairItems 报修物品领域对象列表
     * @return 报修物品响应DTO列表
     */
    public List<RepairItemResponseDTO> toRepairItemResponseDTOs(List<RepairItem> repairItems) {
        return repairItems.stream()
                .map(this::toRepairItemResponseDTO)
                .collect(Collectors.toList());
    }
}
