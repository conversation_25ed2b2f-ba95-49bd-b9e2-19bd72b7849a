package com.inboyu.order.app.dto.request.repair;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 运营端受理/不受理报修请求DTO
 */
@Data
@Schema(description = "运营端受理/不受理报修请求")
public class RepairOrderAcceptRejectRequestDTO {

    @Schema(description = "报修工单ID", example = "123456789")
    @NotNull(message = "报修工单ID不能为空")
    private Long repairOrderId;

    @Schema(description = "操作类型 (accept: 受理, reject: 不受理)", example = "accept")
    @NotBlank(message = "操作类型不能为空")
    private String operationType; // "accept" or "reject"

    @Schema(description = "操作说明", example = "已受理，安排维修人员上门")
    private String operationComment;

    @Schema(description = "拒绝理由类型", example = "personal_items", allowableValues = {"personal_items", "human_damage", "consumable", "other"})
    private String rejectReasonType;

    @Schema(description = "操作人ID", example = "1001")
    @NotNull(message = "操作人ID不能为空")
    private Long operatorId;

    @Schema(description = "操作人姓名", example = "张三")
    @NotBlank(message = "操作人姓名不能为空")
    private String operatorName;

    @Schema(description = "操作人类型", example = "user_type.maintainer")
    @NotBlank(message = "操作人类型不能为空")
    private String operatorType;
}
