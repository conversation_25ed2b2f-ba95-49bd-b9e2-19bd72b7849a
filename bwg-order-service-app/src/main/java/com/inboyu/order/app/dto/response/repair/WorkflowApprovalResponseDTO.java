package com.inboyu.order.app.dto.response.repair;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 工作流审批响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "工作流审批响应")
public class WorkflowApprovalResponseDTO {

    @Schema(description = "是否成功", example = "true")
    private Boolean success;

    @Schema(description = "响应消息", example = "审批成功")
    private String message;

    @Schema(description = "工作流实例ID", example = "358429352956092416")
    private String workflowInstanceId;

    @Schema(description = "操作类型", example = "pass")
    private String operateType;

    @Schema(description = "步骤ID", example = "step_001")
    private String stepId;

    @Schema(description = "步骤名称", example = "审批步骤")
    private String name;

    @Schema(description = "审批类型", example = "approval")
    private String approvalType;

    @Schema(description = "状态", example = "completed")
    private String status;

    @Schema(description = "排序", example = "1")
    private Integer sortOrder;

    @Schema(description = "开始时间", example = "2024-01-15 10:00:00")
    private String startTime;

    @Schema(description = "预计完成时间", example = "2024-01-15 12:00:00")
    private String estimatedFinishTime;
}