package com.inboyu.order.app.dto.response.repair;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 工作流审批响应DTO
 */
@Data
public class WorkflowApprovalResponseDTO {

    @Schema(description = "步骤ID")
    private String stepId;

    @Schema(description = "步骤名")
    private String name;

    @Schema(description = "审批类型")
    private String approvalType;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "序号")
    private Integer sortOrder;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "预计完成时间")
    private String estimatedFinishTime;
}
