package com.inboyu.order.app.controller;

import com.inboyu.order.app.application.RepairConfigAppService;
import com.inboyu.order.app.dto.response.repair.RepairItemResponseDTO;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * 报修物品搜索功能测试
 */
@SpringBootTest
public class MiniProgramRepairOrderControllerTest {

    @Autowired
    private RepairConfigAppService repairConfigAppService;

    @Test
    @DisplayName("测试门店报修物品搜索功能")
    void testSearchStoreRepairItems() {
        System.out.println("=== 测试门店报修物品搜索功能 ===");
        
        // 测试数据
        String storeId = "342209989085106176";
        String keyword = "门";
        
        try {
            // 执行搜索
            List<RepairItemResponseDTO> result = repairConfigAppService.searchStoreRepairItems(storeId, keyword);
            
            System.out.println("搜索结果数量: " + result.size());
            
            // 打印搜索结果
            for (RepairItemResponseDTO item : result) {
                System.out.println("物品ID: " + item.getItemId());
                System.out.println("物品分类: " + item.getKind());
                System.out.println("物品名称: " + item.getName());
                System.out.println("排序: " + item.getSortOrder());
                System.out.println("是否启用: " + item.getIsActive());
                System.out.println("---");
            }
            
        } catch (Exception e) {
            System.err.println("搜索失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    @DisplayName("测试空关键词搜索")
    void testSearchStoreRepairItemsWithEmptyKeyword() {
        System.out.println("=== 测试空关键词搜索 ===");
        
        String storeId = "342209989085106176";
        String keyword = "";
        
        try {
            List<RepairItemResponseDTO> result = repairConfigAppService.searchStoreRepairItems(storeId, keyword);
            System.out.println("空关键词搜索结果数量: " + result.size());
            
        } catch (Exception e) {
            System.err.println("搜索失败: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试无效门店ID搜索")
    void testSearchStoreRepairItemsWithInvalidStoreId() {
        System.out.println("=== 测试无效门店ID搜索 ===");
        
        String storeId = "";
        String keyword = "门";
        
        try {
            List<RepairItemResponseDTO> result = repairConfigAppService.searchStoreRepairItems(storeId, keyword);
            System.out.println("无效门店ID搜索结果数量: " + result.size());
            
        } catch (Exception e) {
            System.out.println("预期的异常: " + e.getMessage());
        }
    }
}
