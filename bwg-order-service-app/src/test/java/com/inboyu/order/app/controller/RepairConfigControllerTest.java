package com.inboyu.order.app.controller;

import com.alibaba.fastjson2.JSONObject;
import com.inboyu.order.app.dto.request.repair.RepairConfigCreateRequestDTO;
import com.inboyu.order.app.dto.request.repair.RepairConfigUpdateRequestDTO;
import com.inboyu.order.app.dto.request.repair.RepairTodayEnableDTO;
import com.inboyu.order.app.dto.response.repair.RepairConfigDetailResponseDTO;
import com.inboyu.order.app.dto.response.repair.RepairItemResponseDTO;
import com.inboyu.order.exception.ResponseCode;
import com.inboyu.spring.cloud.starter.common.exception.AppException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 门店报修配置控制器测试
 */
@SpringBootTest
class RepairConfigControllerTest {

    @Autowired
    RepairConfigController repairConfigController;

    private RepairConfigCreateRequestDTO buildCreateDTO(String storeId) {
        RepairConfigCreateRequestDTO dto = new RepairConfigCreateRequestDTO();
        dto.setStoreId(storeId);
        dto.setRepairStartTime(LocalTime.of(9, 0));
        dto.setRepairEndTime(LocalTime.of(18, 0));
        dto.setRepairTimeInterval(3);
        dto.setRepairWeekDay("repair_config_repair_week_day.Monday,repair_config_repair_week_day.Tuesday");
        dto.setMaxRepairDays(7);
        
        RepairTodayEnableDTO todayEnableDTO = new RepairTodayEnableDTO();
        todayEnableDTO.setCode("status.enabled");
        todayEnableDTO.setTitle("启用");
        dto.setRepairTodayEnable(todayEnableDTO);
        
        dto.setRepairItemIds(Arrays.asList("202509120000000036", "202509120000000030", "202509120000000002"));
        return dto;
    }

    @Test
    @DisplayName("测试获取所有报修物品列表")
    void getAllRepairItemsTest() {
        List<RepairItemResponseDTO> response = repairConfigController.getAllRepairItems();
        System.out.println("getAllRepairItems = " + JSONObject.toJSONString(response));
        assertNotNull(response);
    }

    @Test
    @DisplayName("测试创建门店报修配置")
    void createRepairConfigTest() {
        RepairConfigCreateRequestDTO dto = buildCreateDTO("342209989085106176");
        RepairConfigDetailResponseDTO response = repairConfigController.createRepairConfig(dto);
        System.out.println("createRepairConfig = " + JSONObject.toJSONString(response));
        assertNotNull(response);
    }

    @Test
    @DisplayName("测试修改门店报修配置")
    void updateRepairConfigTest() {
        // 先创建配置
        RepairConfigCreateRequestDTO createDTO = buildCreateDTO("342232908947394560");
        RepairConfigDetailResponseDTO createResponse = repairConfigController.createRepairConfig(createDTO);
        String configId = createResponse.getConfigId();
        
        // 修改配置
        RepairConfigUpdateRequestDTO updateDTO = new RepairConfigUpdateRequestDTO();
        updateDTO.setConfigId(configId);
        updateDTO.setStoreId("342232908947394560");
        updateDTO.setRepairStartTime(LocalTime.of(8, 0));
        updateDTO.setRepairEndTime(LocalTime.of(20, 0));
        updateDTO.setRepairTimeInterval(2);
        updateDTO.setRepairWeekDay("repair_config_repair_week_day.Monday,repair_config_repair_week_day.Wednesday");
        updateDTO.setMaxRepairDays(14);
        
        RepairTodayEnableDTO todayEnableDTO = new RepairTodayEnableDTO();
        todayEnableDTO.setCode("status.disabled");
        todayEnableDTO.setTitle("停用");
        updateDTO.setRepairTodayEnable(todayEnableDTO);
        updateDTO.setRepairItemIds(Arrays.asList("202509120000000025", "202509120000000032"));
        
        RepairConfigDetailResponseDTO updateResponse = repairConfigController.updateRepairConfig(updateDTO);
        System.out.println("updateRepairConfig = " + JSONObject.toJSONString(updateResponse));
        
        assertNotNull(updateResponse);
    }

    @Test
    @DisplayName("测试查询门店报修配置详情")
    void getRepairConfigDetailTest() {
        String storeId = "342232908947394560";
        // 查询详情
        RepairConfigDetailResponseDTO detailResponse = repairConfigController.getRepairConfigDetail(storeId);
        System.out.println("getRepairConfigDetail = " + JSONObject.toJSONString(detailResponse));
        
        assertNotNull(detailResponse);
    }

    @Test
    @DisplayName("测试创建配置失败-门店已存在")
    void createRepairConfigWhenStoreExistsTest() {
        RepairConfigCreateRequestDTO dto = buildCreateDTO("342233801595949056");
        // 先创建一次
        repairConfigController.createRepairConfig(dto);
        
        // 再次创建应该失败
        AppException exception = assertThrows(AppException.class, () -> {
            repairConfigController.createRepairConfig(dto);
        });
        assertEquals(ResponseCode.REPAIR_CONFIG_ALREADY_EXIST.getCode(), exception.getCode());
    }

    @Test
    @DisplayName("测试查询配置详情失败-配置不存在")
    void getRepairConfigDetailWhenNotExistsTest() {
        AppException exception = assertThrows(AppException.class, () -> {
            repairConfigController.getRepairConfigDetail("999999");
        });
        assertEquals(ResponseCode.REPAIR_CONFIG_NOT_EXIST.getCode(), exception.getCode());
    }
}
