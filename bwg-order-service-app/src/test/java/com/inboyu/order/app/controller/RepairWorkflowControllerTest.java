package com.inboyu.order.app.controller;

import com.alibaba.fastjson2.JSONObject;
import com.inboyu.order.app.application.impl.RepairWorkflowService;
import com.inboyu.admin.dto.response.workflow.InstanceDTO;
import com.inboyu.order.app.dto.request.repair.CancelWorkflowRequestDTO;
import com.inboyu.order.app.dto.request.repair.WorkflowApprovalRequestDTO;
import com.inboyu.order.app.dto.request.repair.WorkflowCallbackRequestDTO;
import com.inboyu.order.app.dto.response.repair.WorkflowApprovalResponseDTO;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.context.WebApplicationContext;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

@SpringBootTest
public class RepairWorkflowControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private RepairWorkflowService repairWorkflowService;

    private MockMvc mockMvc;

    @Test
    @DisplayName("创建工作流 - 模拟真实报修工单数据")
    void create() {
        // 模拟真实的报修工单数据
        Random random = new Random();
        var businessId = random.nextInt(10000);

        String repairOrderId = "***********33";
        String title = "空调维修工单";
        String description = "客厅空调不制冷，需要维修";
        String userId = "1001";
        String userName = "张三";
        String storeId = "***********";  // 门店ID
        
        System.out.println("=== 测试创建工作流 ===");
        System.out.println("工单ID: " + repairOrderId);
        System.out.println("标题: " + title);
        System.out.println("描述: " + description);
        System.out.println("用户ID: " + userId);
        System.out.println("用户名: " + userName);
        System.out.println("门店ID: " + storeId);
        
        var response = repairWorkflowService.createRepairOrderWorkflow(repairOrderId, title, description, userId, userName, storeId);
        System.out.println("工作流创建结果: " + JSONObject.toJSONString(response));
        
        assertNotNull(response, "工作流实例ID不应为空");
        assertTrue(response.length() > 0, "工作流实例ID不应为空字符串");
    }

    @Test
    @DisplayName("工作流回调 - 根据状态类型处理业务")
    void workflowCallback() throws Exception {
        // 定义测试场景数据 - 按照接口文档规范
        String[][] testScenarios = {
            // {instanceStatus, nodeType, sysRemark, isEnd, category, urgency}
            {"instance_status.approve_pass", "node_type.approval", "jgy通过", "true", "电器维修", "high"},
            {"instance_status.approve_reject", "node_type.approval", "审批拒绝：不符合要求", "true", "水电维修", "normal"},
            {"instance_status.running", "node_type.approval", "流程运行中", "false", "空调维修", "low"},
            {"instance_status.completed", "node_type.end", "流程完成", "true", "门窗维修", "normal"},
            {"instance_status.cancelled", "node_type.cancel", "流程取消：申请人撤销", "true", "其他维修", "normal"}
        };

        // 遍历测试场景
        for (int i = 0; i < testScenarios.length; i++) {
            String[] scenario = testScenarios[i];
            String instanceStatus = scenario[0];
            String nodeType = scenario[1];
            String sysRemark = scenario[2];
            boolean isEnd = Boolean.parseBoolean(scenario[3]);
            String category = scenario[4];
            String urgency = scenario[5];

            System.out.println("\n=== 测试场景 " + (i + 1) + ": " + instanceStatus + " ===");

            // 构建回调请求数据
            WorkflowCallbackRequestDTO request = createWorkflowCallbackRequest(
                "35710147802619084" + i,
                "123456789000" + (i + 1),
                instanceStatus,
                nodeType,
                sysRemark,
                isEnd,
                category,
                urgency
            );

            // 执行测试
            String requestJson = JSONObject.toJSONString(request);
            System.out.println("工作流回调请求数据: " + requestJson);

            // 直接调用服务方法进行测试
            String response = repairWorkflowService.handleWorkflowCallback(request);
            System.out.println("工作流回调响应: " + response);
            
            // 验证结果
            assertNotNull(response, "场景 " + (i + 1) + " 响应不应为空");
            assertEquals("success", response, "场景 " + (i + 1) + " 应返回 success");
        }

        // 测试非报修业务类型
        System.out.println("\n=== 测试场景 6: 非报修业务类型 ===");
        WorkflowCallbackRequestDTO nonRepairRequest = createWorkflowCallbackRequest(
            "357101478026190856",
            "1234567890006",
            "instance_status.approve_pass",
            "node_type.approval",
            "其他业务审批通过",
            true,
            "其他业务",
            "normal"
        );
        nonRepairRequest.setBusinessType("workflow_business_type.other_request"); // 非报修业务类型

        String nonRepairResponse = repairWorkflowService.handleWorkflowCallback(nonRepairRequest);
        System.out.println("非报修业务回调响应: " + nonRepairResponse);
        
        // 验证非报修业务类型应该返回 success 但不会处理具体业务逻辑
        assertNotNull(nonRepairResponse);
        assertEquals("success", nonRepairResponse);
    }

    /**
     * 创建工作流回调请求对象
     */
    private WorkflowCallbackRequestDTO createWorkflowCallbackRequest(
            String instanceId,
            String businessId,
            String instanceStatus,
            String nodeType,
            String sysRemark,
            boolean isEnd,
            String category,
            String urgency) {
        
        WorkflowCallbackRequestDTO request = new WorkflowCallbackRequestDTO();
        request.setInstanceId(instanceId);
        request.setIsChangeInstance(false);
        request.setBusinessId(businessId);
        request.setBusinessType("workflow_business_type.repair_request");
        request.setNodeType(nodeType);
        request.setInstanceStatus(instanceStatus);
        request.setIsEnd(isEnd);
        request.setSysRemark(sysRemark);

        // 设置业务变量
        Map<String, String> variable = new HashMap<>();
        variable.put("urgency", urgency);
        variable.put("category", category);
        variable.put("estimatedCost", "200");
        request.setVariable(variable);

        // 设置表单数据
        Map<String, Object> form = new HashMap<>();
        
        // 根据状态类型设置不同的表单数据 - 按照接口文档格式
        switch (instanceStatus) {
            case "instance_status.approve_pass":
                Map<String, String> approvalData = new HashMap<>();
                approvalData.put("title", "审批意见");
                approvalData.put("value", "同意处理");
                form.put("审批意见", approvalData);
                break;
            case "instance_status.approve_reject":
                Map<String, String> rejectData = new HashMap<>();
                rejectData.put("title", "拒绝原因");
                rejectData.put("value", "申请材料不完整");
                form.put("拒绝原因", rejectData);
                break;
            case "instance_status.running":
                Map<String, String> runningData = new HashMap<>();
                runningData.put("title", "当前状态");
                runningData.put("value", "审批中");
                form.put("当前状态", runningData);
                break;
            case "instance_status.completed":
                Map<String, String> completeData = new HashMap<>();
                completeData.put("title", "完成时间");
                completeData.put("value", "2025-07-11 12:11:11");
                form.put("完成时间", completeData);
                break;
            case "instance_status.cancelled":
                Map<String, String> cancelData = new HashMap<>();
                cancelData.put("title", "取消原因");
                cancelData.put("value", "申请人主动撤销");
                form.put("取消原因", cancelData);
                break;
        }
        
        request.setForm(form);
        return request;
    }

    @Test
    @DisplayName("工作流回调 - 模拟文档示例数据")
    void workflowCallbackWithDocumentExample() {
        // 按照接口文档示例创建测试数据
        WorkflowCallbackRequestDTO request = new WorkflowCallbackRequestDTO();
        request.setInstanceId("357101478026190848");
        request.setIsChangeInstance(true);
        request.setBusinessId("1234567890001");
        request.setBusinessType("workflow_business_type.repair_request");
        request.setNodeType("node_type.approval");
        request.setInstanceStatus("instance_status.approve_pass");
        request.setIsEnd(true);
        request.setSysRemark("jgy通过");

        // 设置业务变量
        Map<String, String> variable = new HashMap<>();
        variable.put("key1", "value1");
        request.setVariable(variable);

        // 设置表单数据 - 按照文档格式
        Map<String, Object> form = new HashMap<>();
        
        Map<String, String> labelData = new HashMap<>();
        labelData.put("title", "其他");
        labelData.put("value", "说明内容");
        form.put("标签key", labelData);
        
        Map<String, String> reasonData = new HashMap<>();
        reasonData.put("title", "原因");
        reasonData.put("value", "原因内容");
        form.put("原因key", reasonData);
        
        Map<String, String> timeData = new HashMap<>();
        timeData.put("title", "时间值");
        timeData.put("value", "2025-07-11 12:11:11");
        form.put("时间Key", timeData);
        
        request.setForm(form);

        // 执行测试
        String requestJson = JSONObject.toJSONString(request);
        System.out.println("=== 文档示例回调请求数据 ===");
        System.out.println(requestJson);
        
        String response = repairWorkflowService.handleWorkflowCallback(request);
        System.out.println("回调响应: " + response);
        
        assertNotNull(response);
        assertEquals("success", response);
    }

    @Test
    @DisplayName("取消工作流 - 模拟文档示例数据")
    void cancelWorkflow() {
        // 按照接口文档示例创建测试数据
        String instanceId = "358429344559095808";
        
        CancelWorkflowRequestDTO request = new CancelWorkflowRequestDTO();
        request.setComment("同意2222");
        request.setUserId("353791563220090880");
        request.setUserName("jgy");
        request.setUserType("user_type.user");
        
        System.out.println("=== 测试取消工作流 ===");
        System.out.println("实例ID: " + instanceId);
        System.out.println("撤销说明: " + request.getComment());
        System.out.println("用户ID: " + request.getUserId());
        System.out.println("用户名: " + request.getUserName());
        System.out.println("用户类型: " + request.getUserType());
        
        String response = repairWorkflowService.cancelWorkflow(instanceId, request);
        System.out.println("取消工作流结果: " + response);
        
        assertNotNull(response, "取消工作流响应不应为空");
        assertEquals("success", response, "取消工作流应返回 success");
    }

    @Test
    @DisplayName("取消工作流 - 不同用户类型测试")
    void cancelWorkflowWithDifferentUserTypes() {
        // 测试不同用户类型的取消工作流
        String[][] testScenarios = {
            // {userType, userId, userName, comment}
            {"user_type.user", "1001", "张三", "用户主动取消"},
            {"user_type.system", "system", "系统管理员", "系统自动取消"},
            {"user_type.customer", "2001", "李四", "客户申请取消"}
        };
        
        for (int i = 0; i < testScenarios.length; i++) {
            String[] scenario = testScenarios[i];
            String userType = scenario[0];
            String userId = scenario[1];
            String userName = scenario[2];
            String comment = scenario[3];
            
            String instanceId = "35842934455909580" + (i + 1);
            
            System.out.println("\n=== 测试场景 " + (i + 1) + ": " + userType + " ===");
            
            CancelWorkflowRequestDTO request = new CancelWorkflowRequestDTO();
            request.setComment(comment);
            request.setUserId(userId);
            request.setUserName(userName);
            request.setUserType(userType);
            
            String requestJson = JSONObject.toJSONString(request);
            System.out.println("取消工作流请求数据: " + requestJson);
            
            String response = repairWorkflowService.cancelWorkflow(instanceId, request);
            System.out.println("取消工作流响应: " + response);
            
            assertNotNull(response, "场景 " + (i + 1) + " 响应不应为空");
            assertEquals("success", response, "场景 " + (i + 1) + " 应返回 success");
        }
    }

    @Test
    @DisplayName("工作流审批 - 模拟文档示例数据")
    void workflowApproval() {
        // 按照接口文档示例创建测试数据
        String instanceStepId = "358429352956092416";
        
        WorkflowApprovalRequestDTO request = new WorkflowApprovalRequestDTO();
        request.setOperateType("pass");
        request.setComment("同意2222");
        request.setUserType("user_type.user");
        request.setUserId("34567876");
        request.setUserName("jgygygyg");
        
        System.out.println("=== 测试工作流审批 ===");
        System.out.println("实例步骤ID: " + instanceStepId);
        System.out.println("操作类型: " + request.getOperateType());
        System.out.println("审批意见: " + request.getComment());
        System.out.println("用户类型: " + request.getUserType());
        System.out.println("用户ID: " + request.getUserId());
        System.out.println("用户名: " + request.getUserName());
        
        WorkflowApprovalResponseDTO response = repairWorkflowService.workflowApproval(instanceStepId, request);
        System.out.println("工作流审批结果: " + JSONObject.toJSONString(response));
        
        assertNotNull(response, "工作流审批响应不应为空");
        assertNotNull(response.getStepId(), "步骤ID不应为空");
        assertNotNull(response.getStatus(), "状态不应为空");
    }

    @Test
    @DisplayName("工作流审批 - 不同操作类型测试")
    void workflowApprovalWithDifferentOperateTypes() {
        // 测试不同操作类型的审批
        String[][] testScenarios = {
            // {operateType, comment, userType, userId, userName}
            {"pass", "同意审批", "user_type.user", "1001", "张三"},
            {"reject", "拒绝审批", "user_type.user", "1002", "李四"},
            {"pass", "系统自动通过", "user_type.system", "system", "系统管理员"}
        };
        
        for (int i = 0; i < testScenarios.length; i++) {
            String[] scenario = testScenarios[i];
            String operateType = scenario[0];
            String comment = scenario[1];
            String userType = scenario[2];
            String userId = scenario[3];
            String userName = scenario[4];
            
            String instanceStepId = "35842935295609241" + (i + 1);
            
            System.out.println("\n=== 测试场景 " + (i + 1) + ": " + operateType + " ===");
            
            WorkflowApprovalRequestDTO request = new WorkflowApprovalRequestDTO();
            request.setOperateType(operateType);
            request.setComment(comment);
            request.setUserType(userType);
            request.setUserId(userId);
            request.setUserName(userName);
            
            String requestJson = JSONObject.toJSONString(request);
            System.out.println("工作流审批请求数据: " + requestJson);
            
            WorkflowApprovalResponseDTO response = repairWorkflowService.workflowApproval(instanceStepId, request);
            System.out.println("工作流审批响应: " + JSONObject.toJSONString(response));
            
            assertNotNull(response, "场景 " + (i + 1) + " 响应不应为空");
            assertNotNull(response.getStepId(), "场景 " + (i + 1) + " 步骤ID不应为空");
            assertNotNull(response.getStatus(), "场景 " + (i + 1) + " 状态不应为空");
        }
    }

    @Test
    @DisplayName("查询工作流实例详情 - 模拟文档示例数据")
    void getWorkflowInstance() {
        // 按照接口文档示例创建测试数据
        String workflowInstanceId = "***********33";
        
        System.out.println("=== 测试查询工作流实例详情 ===");
        System.out.println("工作流实例ID: " + workflowInstanceId);
        
        InstanceDTO response = repairWorkflowService.getWorkflowInstance(workflowInstanceId);
        System.out.println("工作流实例详情: " + JSONObject.toJSONString(response));
        
        // 验证结果
        if (response != null) {
            assertNotNull(response.getInstanceId(), "实例ID不应为空");
            assertNotNull(response.getTitle(), "标题不应为空");
            assertNotNull(response.getStatus(), "状态不应为空");
            assertNotNull(response.getBusinessId(), "业务ID不应为空");
            assertNotNull(response.getBusinessType(), "业务类型不应为空");
            
            System.out.println("工作流实例基本信息:");
            System.out.println("  实例ID: " + response.getInstanceId());
            System.out.println("  标题: " + response.getTitle());
            System.out.println("  业务ID: " + response.getBusinessId());
            System.out.println("  业务类型: " + response.getBusinessType());
            System.out.println("  状态: " + response.getStatus());
            System.out.println("  是否完结: " + response.getIsFinished());
            System.out.println("  发起时间: " + response.getStartTime());
            System.out.println("  发起人: " + response.getCreateUserName());
            System.out.println("  单据网址: " + response.getUrl());
            System.out.println("  备注: " + response.getRemark());
            
            // 验证业务变量
            if (response.getVariables() != null && !response.getVariables().isEmpty()) {
                System.out.println("  业务变量:");
                response.getVariables().forEach((key, value) -> 
                    System.out.println("    " + key + ": " + value));
            }
            
            // 验证当前步骤
            if (response.getCurrentStep() != null) {
                System.out.println("  当前审批步骤:");
                System.out.println("    步骤ID: " + response.getCurrentStep().getStepId());
                System.out.println("    步骤名: " + response.getCurrentStep().getName());
                System.out.println("    状态: " + response.getCurrentStep().getStatus());
                System.out.println("    序号: " + response.getCurrentStep().getSortOrder());
                System.out.println("    开始时间: " + response.getCurrentStep().getStartTime());
                System.out.println("    预计完成时间: " + response.getCurrentStep().getEstimatedFinishTime());
            }
            
            // 验证审批流程
            if (response.getSteps() != null && !response.getSteps().isEmpty()) {
                System.out.println("  审批流程步骤数: " + response.getSteps().size());
                for (int i = 0; i < response.getSteps().size(); i++) {
                    var step = response.getSteps().get(i);
                    System.out.println("    步骤 " + (i + 1) + ":");
                    System.out.println("      步骤ID: " + step.getStepId());
                    System.out.println("      步骤名: " + step.getName());
                    System.out.println("      状态: " + step.getStatus());
                    System.out.println("      序号: " + step.getSortOrder());
                    System.out.println("      开始时间: " + step.getStartTime());
                    System.out.println("      预计完成时间: " + step.getEstimatedFinishTime());
                    
                    // 验证审批详情
                    if (step.getOperateDetails() != null && !step.getOperateDetails().isEmpty()) {
                        System.out.println("      审批详情数: " + step.getOperateDetails().size());
                        for (int j = 0; j < step.getOperateDetails().size(); j++) {
                            var detail = step.getOperateDetails().get(j);
                            System.out.println("        审批详情 " + (j + 1) + ":");
                            System.out.println("          用户类型: " + detail.getUserType());
                            System.out.println("          用户ID: " + detail.getUserId());
                            System.out.println("          用户名: " + detail.getUserName());
                            
                            if (detail.getOperateDetail() != null) {
                                System.out.println("          审批信息:");
                                System.out.println("            说明: " + detail.getOperateDetail().getComment());
                                if (detail.getOperateDetail().getAttachments() != null) {
                                    System.out.println("            附件数: " + detail.getOperateDetail().getAttachments().size());
                                }
                            }
                        }
                    }
                }
            }
        } else {
            System.out.println("未找到工作流实例，可能实例ID不存在或已被删除");
        }
    }

    @Test
    @DisplayName("查询工作流实例详情 - 不同状态测试")
    void getWorkflowInstanceWithDifferentStatus() {
        // 测试不同状态的工作流实例
        String[] testScenarios = {
            "WF123456",  // 正常状态
            "WF123457",  // 审批中状态
            "WF123458",  // 已完成状态
            "WF123459",  // 已取消状态
            "INVALID_ID" // 无效ID
        };
        
        for (int i = 0; i < testScenarios.length; i++) {
            String workflowInstanceId = testScenarios[i];
            
            System.out.println("\n=== 测试场景 " + (i + 1) + ": " + workflowInstanceId + " ===");
            
            try {
                InstanceDTO response = repairWorkflowService.getWorkflowInstance(workflowInstanceId);
                
                if (response != null) {
                    System.out.println("查询成功:");
                    System.out.println("  实例ID: " + response.getInstanceId());
                    System.out.println("  状态: " + response.getStatus());
                    System.out.println("  是否完结: " + response.getIsFinished());
                    System.out.println("  标题: " + response.getTitle());
                    
                    assertNotNull(response.getInstanceId(), "场景 " + (i + 1) + " 实例ID不应为空");
                    assertNotNull(response.getStatus(), "场景 " + (i + 1) + " 状态不应为空");
                } else {
                    System.out.println("未找到工作流实例");
                }
                
            } catch (Exception e) {
                System.out.println("查询失败: " + e.getMessage());
                // 对于无效ID，异常是正常的
                if (!"INVALID_ID".equals(workflowInstanceId)) {
                    throw e;
                }
            }
        }
    }

}
