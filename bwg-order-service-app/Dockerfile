FROM docker-hub.inboyu.com/runtime/amazoncorretto-jre:21.0.5-alpine3.20
ENV PARENT_ID order-service
ENV ARTIFACTID ${PARENT_ID}-app
ENV ARTIFACTVERSION *
ENV HOME_PATH /home
ENV PORT_MAIN 10180
ENV HEAP_DUMP_PATH $HOME_PATH/dumps
ENV CONFIGS_PATH $HOME_PATH/config
ENV GC_LOG_PATH $HOME_PATH/logs/gclogs
ENV GC_LOG_CONFIG time,level,tags:filecount=1,filesize=5M
ENV HEAP_SIZE -Xms256M -Xmx800M
ENV DIRECT_MEM -XX:MaxDirectMemorySize=64m
ENV C2_NEM -XX:ReservedCodeCacheSize=128m
ENV MM_MEN -XX:MaxMetaspaceSize=200m
ENV GC_CONFIG -XX:+UseZGC -XX:+UseCodeCacheFlushing -XX:+UseCompressedOops -XX:+UseCompressedClassPointers -XX:+SegmentedCodeCache -XX:+PrintCommandLineFlags -XX:+ExplicitGCInvokesConcurrent -Dfile.encoding=UTF-8 -Djava.security.egd=file:/dev/./urandom -Dnetworkaddress.cache.ttl=10 -XX:+UseCountedLoopSafepoints -XX:LoopStripMiningIter=1 -XX:+UnlockDiagnosticVMOptions -XX:+UnlockExperimentalVMOptions -XX:-OmitStackTraceInFastThrow -XX:GuaranteedSafepointInterval=0 -XX:+DisableExplicitGC -XX:+SafepointTimeout -XX:SafepointTimeoutDelay=1000
ENV ADD_OPENS --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.io=ALL-UNNAMED --add-opens java.base/java.math=ALL-UNNAMED --add-opens java.base/java.net=ALL-UNNAMED --add-opens java.base/java.nio=ALL-UNNAMED --add-opens java.base/java.security=ALL-UNNAMED --add-opens java.base/java.text=ALL-UNNAMED --add-opens java.base/java.time=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/jdk.internal.access=ALL-UNNAMED --add-opens java.base/jdk.internal.misc=ALL-UNNAMED
RUN mkdir -p $GC_LOG_PATH $HEAP_DUMP_PATH $CONFIGS_PATH
WORKDIR $HOME_PATH
ADD ./target/bwg-$ARTIFACTID-$ARTIFACTVERSION.jar $HOME_PATH/$ARTIFACTID.jar
EXPOSE $PORT_MAIN
ENTRYPOINT exec java -jar $HEAP_SIZE $DIRECT_MEM $C2_NEM $MM_MEN -Xlog:gc*=warning:file=$GC_LOG_PATH/gc.log:$GC_LOG_CONFIG -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$HEAP_DUMP_PATH -DJM.SNAPSHOT.PATH=$CONFIGS_PATH $ADD_OPENS $ARTIFACTID.jar
