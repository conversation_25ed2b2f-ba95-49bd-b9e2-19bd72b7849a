apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: order-service-core
  name: order-service-core
  namespace: franchise-dev
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: order-service-core
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: order-service-core
    spec:
      containers:
        - image: >-
            ${IMAGE_URL}
          imagePullPolicy: Always
          name: order-service-core
          livenessProbe: #探针存活
            httpGet:
              path: /actuator/health/liveness
              port: 10160
            initialDelaySeconds: 40
            successThreshold: 1 # 检测到有1次成功则认为服务是`存活`
            failureThreshold: 3 # 检测到有3次失败则认为服务是`未存活`
            timeoutSeconds: 5 #超时时间
            periodSeconds: 30 #每隔30秒检测一次
          readinessProbe:  #探针就绪
            httpGet:
              path: /actuator/health/readiness
              port: 10160
            initialDelaySeconds: 10
            successThreshold: 1 # 检测到有1次成功则认为服务是`就绪`
            failureThreshold: 10 # 检测到有15次失败则认为服务是`未就绪`
            timeoutSeconds: 5 #超时时间
            periodSeconds: 3 #每隔3秒检测一次
          lifecycle: # 下载必要的dump处理脚本，如发生内存溢出打出dump在重启前会上报文件到阿里云oss，并企业微信通知
            preStop:
              exec:
                command:
                  - /bin/sh
                  - ./dump-alioss.sh
          ports:
            - containerPort: 10160
              name: port-10160
              protocol: TCP
          env:
            - name: SW_AGENT_NAMESPACE
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: metadata.namespace
            - name: HEAP_SIZE
              value: '-Xms256M -Xmx256M'
            - name: DIRECT_MEM
              value: '-XX:MaxDirectMemorySize=16m'
            - name: LOGBACK_CONFIG_PATH  #dev环境专用变量
              value: './config/iby-logback-spring.xml'
          resources:
            limits:
              cpu: '2'
              memory: 600Mi
            requests:
              cpu: 50m
              memory: 300Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          envFrom:
            - secretRef:
                name: app-secret
              prefix: secret_
          volumeMounts:
            - name: date
              mountPath: /etc/localtime
            - name: iby-logback-spring-xml  # 与上面的volumes.name保持一致
              mountPath: /home/<USER>/iby-logback-spring.xml
              subPath: iby-logback-spring.xml
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: deviotsr
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
        - name: date
          hostPath:
            path: /etc/localtime
        - name: iby-logback-spring-xml #开发环境特有配置
          configMap:
            name: iby-logback-spring-xml  # 指定要使用的configmap名称
