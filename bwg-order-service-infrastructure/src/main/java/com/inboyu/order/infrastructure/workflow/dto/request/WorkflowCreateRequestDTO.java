package com.inboyu.order.infrastructure.workflow.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.HashMap;

/**
 * 工作流创建请求DTO
 * 
 * <AUTHOR>
 */
@Data
public class WorkflowCreateRequestDTO {

    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "业务类型不能为空")
    private String businessType;

    @Schema(description = "业务唯一id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "业务唯一id不能为空")
    private String businessId;

    @Schema(description = "工作流标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "工作流标题不能为空")
    private String title;

    @Schema(description = "单据网址")
    private String url = "";

    @Schema(description = "工作流摘要")
    private String remark = "";

    @Schema(description = "初始参数")
    private HashMap<String, String> initParams = new HashMap<>();

    @Schema(description = "项目ID")
    private String storeId = "0";

    @Schema(description = "用户类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户类型不能为空")
    private String userType;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户ID不能为空")
    private String userId;

    @Schema(description = "用户名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户名不能为空")
    private String userName;

    @Schema(description = "表单数据Json")
    private String form = "";
}
