package com.inboyu.order.infrastructure.builder.impl;

import com.inboyu.order.domain.repair.model.RepairItem;
import com.inboyu.order.domain.repair.model.RepairItemId;
import com.inboyu.order.infrastructure.builder.RepairItemBuilder;
import com.inboyu.order.infrastructure.dao.entity.RepairItemEntity;
import com.inboyu.constant.DeleteFlag;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 报修物品转换器实现
 * 负责领域对象与数据库实体之间的转换
 */
@Component
public class RepairItemBuilderImpl implements RepairItemBuilder {

    @Override
    public RepairItem toDomain(RepairItemEntity entity) {
        if (entity == null) {
            return null;
        }

        return RepairItem.builder()
                .id(RepairItemId.of(entity.getItemId()))
                .kind(entity.getKind())
                .name(entity.getName())
                .icon(entity.getIcon())
                .url(entity.getUrl())
                .remark(entity.getRemark())
                .sortOrder(entity.getSortOrder())
                .isActive(entity.getIsActive())
                .build();
    }

    @Override
    public RepairItemEntity toEntity(RepairItem repairItem) {
        if (repairItem == null) {
            return null;
        }

        RepairItemEntity entity = new RepairItemEntity();
        entity.setItemId(repairItem.getId().getValue());
        entity.setKind(repairItem.getKind());
        entity.setName(repairItem.getName());
        entity.setIcon(repairItem.getIcon());
        entity.setUrl(repairItem.getUrl());
        entity.setRemark(repairItem.getRemark());
        entity.setSortOrder(repairItem.getSortOrder());
        entity.setIsActive(repairItem.getIsActive());

        // 设置基础字段
        entity.setCreateTime(LocalDateTime.now());
        entity.setModifyTime(LocalDateTime.now());
        entity.setDeleted(DeleteFlag.NOT_DELETED);
        entity.setVersion(0L);

        return entity;
    }
}
