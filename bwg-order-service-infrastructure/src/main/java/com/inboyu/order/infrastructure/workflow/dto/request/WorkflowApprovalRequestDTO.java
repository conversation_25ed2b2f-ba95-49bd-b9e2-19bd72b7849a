package com.inboyu.order.infrastructure.workflow.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.HashMap;

/**
 * 工作流审批请求DTO
 * 
 * <AUTHOR>
 */
@Data
public class WorkflowApprovalRequestDTO {

    @Schema(description = "操作类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "操作类型不能为空")
    private String operateType;

    @Schema(description = "审批意见")
    private String comment = "";

    @Schema(description = "用户类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户类型不能为空")
    private String userType;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户ID不能为空")
    private String userId;

    @Schema(description = "用户名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户名不能为空")
    private String userName;

    @Schema(description = "表单数据")
    private HashMap<String, String> form = new HashMap<>();
}
