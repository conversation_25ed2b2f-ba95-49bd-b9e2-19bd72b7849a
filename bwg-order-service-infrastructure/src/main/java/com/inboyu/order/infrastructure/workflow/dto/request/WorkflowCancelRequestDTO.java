package com.inboyu.order.infrastructure.workflow.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 工作流撤销请求DTO
 * 
 * <AUTHOR>
 */
@Data
public class WorkflowCancelRequestDTO {

    @Schema(description = "撤销原因", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "撤销原因不能为空")
    private String comment;

    @Schema(description = "用户类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户类型不能为空")
    private String userType;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户ID不能为空")
    private String userId;

    @Schema(description = "用户名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户名不能为空")
    private String userName;
}
