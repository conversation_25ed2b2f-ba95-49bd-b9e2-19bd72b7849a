package com.inboyu.order.infrastructure.service;

import com.inboyu.order.domain.repair.model.*;
import com.inboyu.order.domain.repair.service.RepairOrderDomainService;
import com.inboyu.order.domain.repair.service.RepairProgressDomainService;
import com.inboyu.order.domain.repair.service.RepairProgressTextService;
import com.inboyu.order.domain.repair.service.WorkflowEventDomainService;
import com.inboyu.order.domain.repair.service.WorkflowConsistencyDomainService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * 工作流事件领域服务实现
 * 负责处理工作流相关的业务逻辑
 * 位于infrastructure层，可以处理事务和数据库交互
 */
@Service
public class WorkflowEventDomainServiceImpl implements WorkflowEventDomainService {

    @Autowired
    private RepairOrderDomainService repairOrderDomainService;

    @Autowired
    private RepairProgressDomainService repairProgressDomainService;

    @Autowired
    private RepairProgressTextService repairProgressTextService;

    @Autowired
    private WorkflowConsistencyDomainService workflowConsistencyDomainService;

    @Override
    public void handleWorkflowCreateEvent(String businessId, String businessType) {
        // 检查是否为报修业务类型
        if (!isRepairBusinessType(businessType)) {
            return;
        }

        // 获取报修订单
        RepairOrder repairOrder = getRepairOrderByBusinessId(businessId);
        if (repairOrder == null) {
            return;
        }

        // 创建提交进度记录
        repairProgressDomainService.createSubmitProgress(repairOrder);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleWorkflowApprovalEvent(String businessId, String businessType, String operateType, 
                                          Long operatorId, String operatorName, String comment, 
                                          String instanceStatus, Map<String, String> variables) {
        // 检查是否为报修业务类型
        if (!isRepairBusinessType(businessType)) {
            return;
        }

        // 获取报修订单
        RepairOrder repairOrder = getRepairOrderByBusinessId(businessId);
        if (repairOrder == null) {
            return;
        }

        // 验证状态一致性
        if (!workflowConsistencyDomainService.validateStatusConsistency(repairOrder, instanceStatus, "node_type.approval")) {
            throw new IllegalStateException("工作流状态与订单状态不一致");
        }

        // 根据审批结果更新订单状态
        RepairOrder updatedOrder = updateRepairOrderByApproval(repairOrder, operateType, operatorId, comment);

        // 再次验证更新后的状态一致性
        RepairOrder finalOrder = workflowConsistencyDomainService.updateOrderStatusByWorkflow(
            updatedOrder, instanceStatus, "node_type.approval");

        // 生成进度记录
        RepairProgress progress = generateWorkflowProgress(
            finalOrder,
            "node_type.approval",
            instanceStatus,
            null, // 表单数据
            variables,
            comment,
            operatorId,
            operatorName
        );

        // 保存进度记录
        repairProgressDomainService.createAcceptRejectProgress(finalOrder, operateType, comment);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleWorkflowCompleteEvent(String businessId, String businessType, Long operatorId, String operatorName) {
        // 检查是否为报修业务类型
        if (!isRepairBusinessType(businessType)) {
            return;
        }

        // 获取报修订单
        RepairOrder repairOrder = getRepairOrderByBusinessId(businessId);
        if (repairOrder == null) {
            return;
        }

        // 验证状态一致性
        if (!workflowConsistencyDomainService.validateStatusConsistency(repairOrder, "instance_status.completed", "node_type.complete")) {
            throw new IllegalStateException("工作流完成状态与订单状态不一致");
        }

        // 更新订单状态为已完成
        RepairOrder updatedOrder = repairOrder.complete(operatorId, "工作流完成", 
            java.time.LocalDateTime.now().toString());

        // 确保状态一致性
        RepairOrder finalOrder = workflowConsistencyDomainService.updateOrderStatusByWorkflow(
            updatedOrder, "instance_status.completed", "node_type.complete");

        // 创建完成进度记录
        repairProgressDomainService.createCompleteProgress(finalOrder, operatorName, "工作流完成");
    }

    @Override
    public boolean isRepairBusinessType(String businessType) {
        return RepairProgressTextService.BUSINESS_TYPE_REPAIR_REQUEST.equals(businessType);
    }

    @Override
    public RepairOrder getRepairOrderByBusinessId(String businessId) {
        try {
            RepairOrderId orderId = RepairOrderId.of(Long.parseLong(businessId));
            return repairOrderDomainService.getRepairOrderDetail(orderId);
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public RepairOrder updateRepairOrderByApproval(RepairOrder repairOrder, String operateType, Long operatorId, String comment) {
        switch (operateType) {
            case "accept":
                return repairOrder.accept(operatorId, comment);
            case "reject":
                return repairOrder.reject(operatorId, comment);
            case "transfer":
                // 转交逻辑，这里可以根据实际业务需求扩展
                return repairOrder.toBuilder()
                    .responsibleId(ResponsibleId.of(operatorId))
                    .remark(comment)
                    .build();
            case "suspend":
                // 挂起逻辑，这里可以根据实际业务需求扩展
                return repairOrder.toBuilder()
                    .suspendReason(comment)
                    .build();
            default:
                return repairOrder;
        }
    }

    @Override
    public RepairProgress generateWorkflowProgress(RepairOrder repairOrder, String nodeType, String instanceStatus,
                                                 Map<String, Object> form, Map<String, String> variable,
                                                 String sysRemark, Long operatorId, String operatorName) {
        
        // 生成进度文案
        RepairProgressTextService.ProgressText progressText = repairProgressTextService.generateProgressText(
            repairOrder, nodeType, instanceStatus, form, variable, sysRemark, false);

        // 生成进度ID
        RepairProgressId progressId = repairProgressDomainService.generateProgressId();

        // 构建进度记录
        return RepairProgress.builder()
            .progressId(progressId)
            .repairOrderId(repairOrder.getRepairOrderId())
            .operatorId(OperatorId.of(operatorId))
            .operatorName(operatorName)
            .detail(progressText.getMainText())
            .subDetail(progressText.getSubText())
            .operatorType(progressText.getOperatorType())
            .status(ProgressStatus.of(getProgressStatusByNodeType(nodeType, instanceStatus)))
            .comment(sysRemark)
            .isReopen(IsReopen.of(0))
            .reopenReason("")
            .createTime(java.time.LocalDateTime.now())
            .modifyTime(java.time.LocalDateTime.now())
            .build();
    }

    /**
     * 根据节点类型和实例状态获取进度状态
     */
    private String getProgressStatusByNodeType(String nodeType, String instanceStatus) {
        switch (nodeType) {
            case "node_type.submit":
                return "repair_progress_status.submitted";
            case "node_type.approval":
                if ("instance_status.approve_pass".equals(instanceStatus)) {
                    return "repair_progress_status.accepted";
                } else if ("instance_status.approve_reject".equals(instanceStatus)) {
                    return "repair_progress_status.rejected";
                } else {
                    return "repair_progress_status.processing";
                }
            case "node_type.complete":
                return "repair_progress_status.completed";
            case "node_type.confirm":
                if ("instance_status.confirmed".equals(instanceStatus)) {
                    return "repair_progress_status.confirmed";
                } else if ("instance_status.rework".equals(instanceStatus)) {
                    return "repair_progress_status.reopened";
                } else {
                    return "repair_progress_status.processing";
                }
            default:
                return "repair_progress_status.processing";
        }
    }
}
