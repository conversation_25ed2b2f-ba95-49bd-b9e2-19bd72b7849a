package com.inboyu.order.infrastructure.builder;

import com.inboyu.order.domain.repair.model.RepairItem;
import com.inboyu.order.infrastructure.dao.entity.RepairItemEntity;

/**
 * 报修物品转换器接口
 * 负责领域对象与数据库实体之间的转换
 */
public interface RepairItemBuilder {

    /**
     * 将数据库实体转换为领域对象
     *
     * @param entity 报修物品数据库实体
     * @return 报修物品领域对象
     */
    RepairItem toDomain(RepairItemEntity entity);

    /**
     * 将领域对象转换为数据库实体
     *
     * @param repairItem 报修物品领域对象
     * @return 报修物品数据库实体
     */
    RepairItemEntity toEntity(RepairItem repairItem);
}
