package com.inboyu.order.infrastructure.dao;

import com.inboyu.order.infrastructure.dao.entity.RepairStoreItemEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 门店报修物品配置数据访问接口
 */
@Repository
public interface RepairStoreItemDao extends JpaRepository<RepairStoreItemEntity, Long> {

    /**
     * 根据门店ID查询门店可报修物品
     *
     * @param configId 配置ID
     * @param deleted  删除标志
     * @return 门店报修物品配置实体列表
     */
    List<RepairStoreItemEntity> findByStoreIdAndDeleted(Long storeId, Integer deleted);

    /**
     * 根据配置ID和门店ID查询门店可报修物品
     *
     * @param configId 配置ID
     * @param storeId  门店ID
     * @param deleted  删除标志
     * @return 门店报修物品配置实体列表
     */
    List<RepairStoreItemEntity> findByConfigIdAndStoreIdAndDeleted(Long configId, Long storeId, Integer deleted);

    /**
     * 逻辑删除指定配置的所有门店报修物品
     *
     * @param configId 配置ID
     * @param storeId  门店ID
     */
    @Modifying
    @Query("UPDATE RepairStoreItemEntity e SET e.deleted = 1, e.modifyTime = CURRENT_TIMESTAMP " +
           "WHERE e.configId = :configId AND e.storeId = :storeId AND e.deleted = 0")
    void logicalDeleteByConfigIdAndStoreId(@Param("configId") Long configId, @Param("storeId") Long storeId);
}
