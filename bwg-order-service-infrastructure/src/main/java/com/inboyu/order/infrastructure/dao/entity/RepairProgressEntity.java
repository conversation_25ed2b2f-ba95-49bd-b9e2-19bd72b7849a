package com.inboyu.order.infrastructure.dao.entity;

import com.inboyu.spring.cloud.starter.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 报修进度实体类
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "t_repair_progress")
public class RepairProgressEntity extends BaseEntity {

    @Column(name = "progress_id", nullable = false)
    private Long progressId;

    @Column(name = "repair_order_id", nullable = false)
    private Long repairOrderId;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "user_name", nullable = false, length = 50)
    private String userName;

    @Column(name = "detail", nullable = false, length = 500)
    private String detail;

    @Column(name = "type", nullable = false, length = 200)
    private String type;

    @Column(name = "status", nullable = false, length = 200)
    private String status;

    @Column(name = "comment", nullable = false, length = 500)
    private String comment;

    @Column(name = "distribute_reason", nullable = false, length = 200)
    private String distributeReason;

    @Column(name = "distribute_detail", nullable = false, length = 200)
    private String distributeDetail;

    @Column(name = "is_reopen", nullable = false)
    private Boolean isReopen;

    @Column(name = "reopen_reason", nullable = false, length = 500)
    private String reopenReason;

    @Column(name = "process_method", nullable = false, length = 200)
    private String processMethod;

    @Column(name = "assignee_id", nullable = false)
    private Long assigneeId;

    @Column(name = "assignee_name", nullable = false, length = 50)
    private String assigneeName;
}
