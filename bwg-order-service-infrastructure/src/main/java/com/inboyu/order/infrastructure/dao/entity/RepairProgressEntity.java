package com.inboyu.order.infrastructure.dao.entity;

import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * 报修进度数据库实体
 */
@Data
@Entity
@Table(name = "t_repair_progress")
public class RepairProgressEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "progress_id", nullable = false, unique = true)
    private Long progressId;

    @Column(name = "repair_order_id", nullable = false)
    private Long repairOrderId;

    @Column(name = "operator_id", nullable = false)
    private Long operatorId;

    @Column(name = "operator_name", nullable = false)
    private String operatorName;

    @Column(name = "detail", nullable = false)
    private String detail;

    @Column(name = "sub_detail", nullable = false)
    private String subDetail;

    @Column(name = "operator_type", nullable = false)
    private String operatorType;

    @Column(name = "status", nullable = false)
    private String status;

    @Column(name = "comment", nullable = false)
    private String comment;

    @Column(name = "is_reopen", nullable = false)
    private Integer isReopen;

    @Column(name = "reopen_reason", nullable = false)
    private String reopenReason;

    @CreationTimestamp
    @Column(name = "create_time", nullable = false, updatable = false)
    private LocalDateTime createTime;

    @UpdateTimestamp
    @Column(name = "modify_time", nullable = false)
    private LocalDateTime modifyTime;

    @Version
    @Column(name = "version", nullable = false)
    private Long version;

    @Column(name = "deleted", nullable = false)
    private Integer deleted = 0;
}
