package com.inboyu.order.infrastructure.builder;

import com.inboyu.order.domain.repair.model.RepairOrder;
import com.inboyu.order.infrastructure.entity.RepairOrderEntity;

import java.util.List;

/**
 * 报修工单Builder接口
 * 用于在领域模型和实体类之间进行转换
 */
public interface RepairOrderBuilder {

    /**
     * 将实体类转换为领域模型
     */
    RepairOrder toDomain(RepairOrderEntity entity);

    /**
     * 将领域模型转换为实体类
     */
    RepairOrderEntity toEntity(RepairOrder domain);

    /**
     * 批量将实体类列表转换为领域模型列表
     */
    List<RepairOrder> toDomains(List<RepairOrderEntity> entities);

    /**
     * 批量将领域模型列表转换为实体类列表
     */
    List<RepairOrderEntity> toEntities(List<RepairOrder> domains);
}
