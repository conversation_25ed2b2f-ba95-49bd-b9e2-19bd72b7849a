package com.inboyu.order.infrastructure.builder;

import com.inboyu.order.domain.repair.model.RepairOrder;
import com.inboyu.order.infrastructure.dao.entity.RepairAttachmentEntity;
import com.inboyu.order.infrastructure.dao.entity.RepairOrderEntity;

import java.util.List;

public interface RepairOrderBuilder {
    RepairOrderEntity toEntity(RepairOrder repairOrder);

    RepairOrder toDomain(RepairOrderEntity entity, List<RepairAttachmentEntity> attachmentEntities);

}
