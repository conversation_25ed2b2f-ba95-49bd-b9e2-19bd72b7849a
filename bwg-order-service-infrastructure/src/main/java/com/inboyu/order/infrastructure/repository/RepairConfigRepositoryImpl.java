package com.inboyu.order.infrastructure.repository;

import com.inboyu.order.domain.repair.model.RepairConfig;
import com.inboyu.order.domain.repair.model.RepairConfigId;
import com.inboyu.order.domain.repair.model.RepairItemId;
import com.inboyu.order.domain.repair.model.StoreId;
import com.inboyu.order.domain.repair.repository.RepairConfigRepository;
import com.inboyu.order.infrastructure.builder.RepairConfigBuilder;
import com.inboyu.order.infrastructure.dao.RepairConfigDao;
import com.inboyu.order.infrastructure.dao.RepairStoreItemDao;
import com.inboyu.order.infrastructure.dao.entity.RepairConfigEntity;
import com.inboyu.order.infrastructure.dao.entity.RepairStoreItemEntity;
import com.inboyu.constant.DeleteFlag;
import com.inboyu.spring.cloud.starter.redis.utils.id.RedisSnowflakeIdGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 报修配置仓储实现
 * 实现报修配置的数据访问逻辑
 */
@Repository
public class RepairConfigRepositoryImpl implements RepairConfigRepository {

    @Autowired
    private RepairConfigDao repairConfigDao;

    @Autowired
    private RepairStoreItemDao repairStoreItemDao;

    @Autowired
    private RepairConfigBuilder repairConfigBuilder;

    @Autowired
    private RedisSnowflakeIdGenerator snowflakeIdGenerator;

    @Override
    public RepairConfigId generateId() {
        return RepairConfigId.of(snowflakeIdGenerator.nextId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RepairConfig save(RepairConfig repairConfig) {
        RepairConfigEntity entity = repairConfigBuilder.toEntity(repairConfig);
        RepairConfigEntity savedEntity = repairConfigDao.save(entity);
        return repairConfigBuilder.toDomain(savedEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RepairConfig update(RepairConfig repairConfig) {
        RepairConfigEntity entity = repairConfigBuilder.toUpdateEntity(repairConfig);
        repairConfigDao.update(entity);
        
        // 查询更新后的实体
        RepairConfigEntity updatedEntity = repairConfigDao.findByConfigIdAndDeleted(
                repairConfig.getId().getValue(), DeleteFlag.NOT_DELETED);

        return repairConfigBuilder.toDomain(updatedEntity);
    }

    @Override
    public RepairConfig findById(RepairConfigId configId) {
        if (configId == null) {
            return null;
        }

        RepairConfigEntity entity = repairConfigDao.findByConfigIdAndDeleted(
                configId.getValue(), DeleteFlag.NOT_DELETED);
        return repairConfigBuilder.toDomain(entity);
    }

    @Override
    public RepairConfig findByStoreId(StoreId storeId) {
        if (storeId == null) {
            return null;
        }

        RepairConfigEntity entity = repairConfigDao.findByStoreIdAndDeleted(
                storeId.getValue(), DeleteFlag.NOT_DELETED);
        return repairConfigBuilder.toDomain(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveStoreRepairItems(RepairConfigId configId, StoreId storeId, List<RepairItemId> repairItemIds) {
        if (repairItemIds == null || repairItemIds.isEmpty()) {
            return;
        }

        List<RepairStoreItemEntity> entities = repairItemIds.stream()
                .map(itemId -> createStoreItemEntity(configId, storeId, itemId))
                .collect(Collectors.toList());

        repairStoreItemDao.saveAll(entities);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStoreRepairItems(RepairConfigId configId, StoreId storeId, List<RepairItemId> repairItemIds) {
        // 先逻辑删除原有的配置
        repairStoreItemDao.logicalDeleteByConfigIdAndStoreId(configId.getValue(), storeId.getValue());

        // 再新增新的配置
        if (repairItemIds != null && !repairItemIds.isEmpty()) {
            saveStoreRepairItems(configId, storeId, repairItemIds);
        }
    }

    @Override
    public List<RepairItemId> findRepairItemIdsByStoreId(StoreId storeId) {
        if (storeId == null) {
            return List.of();
        }

        List<RepairStoreItemEntity> entities = repairStoreItemDao.findByStoreIdAndDeleted(
                storeId.getValue(), DeleteFlag.NOT_DELETED);

        return entities.stream()
                .map(entity -> RepairItemId.of(entity.getItemId()))
                .collect(Collectors.toList());
    }

    /**
     * 创建门店报修物品配置实体
     *
     * @param configId 配置ID
     * @param storeId  门店ID
     * @param itemId   物品ID
     * @return 门店报修物品配置实体
     */
    private RepairStoreItemEntity createStoreItemEntity(RepairConfigId configId, StoreId storeId, RepairItemId itemId) {
        RepairStoreItemEntity entity = new RepairStoreItemEntity();
        entity.setConfigId(configId.getValue());
        entity.setStoreId(storeId.getValue());
        entity.setItemId(itemId.getValue());
        entity.setCreateTime(LocalDateTime.now());
        entity.setModifyTime(LocalDateTime.now());
        entity.setDeleted(DeleteFlag.NOT_DELETED);
        entity.setVersion(0L);
        return entity;
    }
}
