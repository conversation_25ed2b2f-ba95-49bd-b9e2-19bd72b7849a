package com.inboyu.order.infrastructure.builder;

import com.inboyu.order.domain.repair.model.RepairProgress;
import com.inboyu.order.infrastructure.entity.RepairProgressEntity;

import java.util.List;

/**
 * 报修进度Builder接口
 * 用于在领域模型和实体类之间进行转换
 */
public interface RepairProgressBuilder {

    /**
     * 将实体类转换为领域模型
     */
    RepairProgress toDomain(RepairProgressEntity entity);

    /**
     * 将领域模型转换为实体类
     */
    RepairProgressEntity toEntity(RepairProgress domain);

    /**
     * 批量将实体类列表转换为领域模型列表
     */
    List<RepairProgress> toDomains(List<RepairProgressEntity> entities);

    /**
     * 批量将领域模型列表转换为实体类列表
     */
    List<RepairProgressEntity> toEntities(List<RepairProgress> domains);
}
