package com.inboyu.order.infrastructure.builder.impl;

import com.inboyu.order.domain.repair.model.*;
import com.inboyu.order.infrastructure.builder.RepairAttachmentBuilder;
import com.inboyu.order.infrastructure.builder.RepairOrderBuilder;
import com.inboyu.order.infrastructure.dao.entity.RepairAttachmentEntity;
import com.inboyu.order.infrastructure.dao.entity.RepairOrderEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 报修订单Builder类
 * 负责领域对象和数据库实体之间的转换
 */
@Component
public class RepairOrderBuilderImpl implements RepairOrderBuilder {

    @Autowired
    private RepairAttachmentBuilder repairAttachmentBuilder;

    /**
     * 将领域对象转换为数据库实体
     */
    public RepairOrderEntity toEntity(RepairOrder repairOrder) {
        RepairOrderEntity entity = new RepairOrderEntity();

        entity.setRepairOrderId(repairOrder.getRepairOrderId().getValue());
        entity.setCustomerId(repairOrder.getCustomerId().getValue());
        entity.setStoreId(repairOrder.getStoreId().getValue());
        entity.setRoomId(repairOrder.getRoomId().getValue());
        entity.setBuildingId(repairOrder.getBuildingId().getValue());
        entity.setItemId(repairOrder.getRepairItemId().getValue());
        entity.setItemTag(repairOrder.getItemTag());
        entity.setWorkflowId(repairOrder.getWorkflowId().getValue());
        entity.setRoomNumber(repairOrder.getRoomNumber());
        entity.setArea(repairOrder.getArea());
        entity.setDetail(repairOrder.getDetail());
        entity.setContactPhone(repairOrder.getContactPhone());
        entity.setSubmitter(repairOrder.getSubmitter());
        entity.setWillRepairDate(repairOrder.getWillRepairDate());
        entity.setWillRepairStart(repairOrder.getWillRepairStart());
        entity.setWillRepairEnd(repairOrder.getWillRepairEnd());
        entity.setIsEnter(repairOrder.getIsEnter().getValue());
        entity.setStatus(repairOrder.getStatus().getCode());
        entity.setOrderType(repairOrder.getOrderType().getCode());
        entity.setTagStatus(repairOrder.getTagStatus().getCode());
        entity.setRefuseReason(repairOrder.getRefuseReason());
        entity.setSuspendReason(repairOrder.getSuspendReason());
        entity.setPreDoorTime(repairOrder.getPreDoorTime());
        entity.setPreRepairTime(repairOrder.getPreRepairTime());
        entity.setFinishTime(repairOrder.getFinishTime());
        entity.setDealTime(repairOrder.getDealTime());
        entity.setResponsibleId(repairOrder.getResponsibleId().getValue());
        entity.setMaintainerId(repairOrder.getMaintainerId().getValue());
        entity.setRemark(repairOrder.getRemark());

        return entity;
    }

    /**
     * 将数据库实体转换为领域对象
     */
    public RepairOrder toDomain(RepairOrderEntity entity, List<RepairAttachmentEntity> attachmentEntities) {
        List<RepairAttachment> attachments = null;
        if (attachmentEntities != null) {
            attachments = attachmentEntities.stream()
                    .map(repairAttachmentBuilder::toDomain)
                    .collect(Collectors.toList());
        }

        return RepairOrder.builder()
                .repairOrderId(RepairOrderId.of(entity.getRepairOrderId()))
                .customerId(CustomerId.of(entity.getCustomerId()))
                .storeId(StoreId.of(entity.getStoreId()))
                .roomId(RoomId.of(entity.getRoomId()))
                .buildingId(BuildingId.of(entity.getBuildingId()))
                .repairItemId(RepairItemId.of(entity.getItemId()))
                .itemTag(entity.getItemTag())
                .workflowId(WorkflowId.of(entity.getWorkflowId()))
                .roomNumber(entity.getRoomNumber())
                .area(entity.getArea())
                .detail(entity.getDetail())
                .contactPhone(entity.getContactPhone())
                .submitter(entity.getSubmitter())
                .willRepairDate(entity.getWillRepairDate())
                .willRepairStart(entity.getWillRepairStart())
                .willRepairEnd(entity.getWillRepairEnd())
                .isEnter(IsEnter.of(entity.getIsEnter()))
                .status(RepairOrderStatus.builder()
                        .code(entity.getStatus())
                        .value(getStatusValue(entity.getStatus()))
                        .build())
                .orderType(OrderType.builder()
                        .code(entity.getOrderType())
                        .value(getOrderTypeValue(entity.getOrderType()))
                        .build())
                .tagStatus(TagStatus.builder()
                        .code(entity.getTagStatus())
                        .value(getTagStatusValue(entity.getTagStatus()))
                        .build())
                .refuseReason(entity.getRefuseReason())
                .suspendReason(entity.getSuspendReason())
                .preDoorTime(entity.getPreDoorTime())
                .preRepairTime(entity.getPreRepairTime())
                .finishTime(entity.getFinishTime())
                .dealTime(entity.getDealTime())
                .responsibleId(ResponsibleId.of(entity.getResponsibleId()))
                .maintainerId(MaintainerId.of(entity.getMaintainerId()))
                .remark(entity.getRemark())
                .createTime(entity.getCreateTime())
                .attachments(attachments)
                .build();
    }

    // 辅助方法：获取状态描述
    private String getStatusValue(String code) {
        switch (code) {
            case RepairOrderStatus.REPAIR_ORDER_STATUS_SUBMITTED:
                return "已提单";
            case RepairOrderStatus.REPAIR_ORDER_STATUS_PROCESSING:
                return "处理中";
            case RepairOrderStatus.REPAIR_ORDER_STATUS_COMPLETED:
                return "已完成";
            case RepairOrderStatus.REPAIR_ORDER_STATUS_CLOSED:
                return "已关闭";
            default:
                return "未知状态";
        }
    }

    private String getOrderTypeValue(String code) {
        if ("repair_order_type.resident".equals(code)) {
            return "住户报修";
        }
        return "未知类型";
    }

    private String getTagStatusValue(String code) {
        if ("repair_tag_status.normal".equals(code)) {
            return "正常";
        }
        return "未知状态";
    }
}
