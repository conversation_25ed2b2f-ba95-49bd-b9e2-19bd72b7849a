package com.inboyu.order.infrastructure.builder;

import com.inboyu.order.domain.repair.model.RepairConfig;
import com.inboyu.order.infrastructure.dao.entity.RepairConfigEntity;

/**
 * 报修配置转换器接口
 * 负责领域对象与数据库实体之间的转换
 */
public interface RepairConfigBuilder {

    /**
     * 将领域对象转换为数据库实体
     *
     * @param repairConfig 报修配置领域对象
     * @return 报修配置数据库实体
     */
    RepairConfigEntity toEntity(RepairConfig repairConfig);

    /**
     * 将数据库实体转换为领域对象
     *
     * @param entity 报修配置数据库实体
     * @return 报修配置领域对象
     */
    RepairConfig toDomain(RepairConfigEntity entity);

    /**
     * 将领域对象转换为更新用的数据库实体
     *
     * @param repairConfig 报修配置领域对象
     * @return 报修配置数据库实体
     */
    RepairConfigEntity toUpdateEntity(RepairConfig repairConfig);
}
