package com.inboyu.order.infrastructure.dao.entity;

import com.inboyu.spring.cloud.starter.jpa.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.time.LocalTime;

/**
 * 报修配置实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_repair_config")
@DynamicInsert
@DynamicUpdate
public class RepairConfigEntity extends BaseEntity {

    @Column(name = "config_id", nullable = false)
    private Long configId; // 配置业务ID

    @Column(name = "store_id", nullable = false)
    private Long storeId; // 门店ID

    @Column(name = "repair_start_time", nullable = false)
    private LocalTime repairStartTime; // 报修开始时间

    @Column(name = "repair_end_time", nullable = false)
    private LocalTime repairEndTime; // 报修结束时间

    @Column(name = "repair_time_interval", nullable = false)
    private Integer repairTimeInterval; // 报修时段时长

    @Column(name = "repair_week_day", nullable = false)
    private String repairWeekDay; // 每周服务日，字典维护

    @Column(name = "max_repair_days", nullable = false)
    private Integer maxRepairDays; // 最远可预约日期（从次日开始算天数）

    @Column(name = "repair_today_enable", nullable = false)
    private String repairTodayEnable; // 是否开放当天预约，字段维护
}
