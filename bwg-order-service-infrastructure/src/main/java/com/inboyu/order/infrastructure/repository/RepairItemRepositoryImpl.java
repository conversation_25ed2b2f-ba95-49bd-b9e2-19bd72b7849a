package com.inboyu.order.infrastructure.repository;

import com.inboyu.order.domain.repair.model.RepairItem;
import com.inboyu.order.domain.repair.model.RepairItemId;
import com.inboyu.order.domain.repair.repository.RepairItemRepository;
import com.inboyu.order.infrastructure.builder.RepairItemBuilder;
import com.inboyu.order.infrastructure.dao.RepairItemDao;
import com.inboyu.order.infrastructure.dao.entity.RepairItemEntity;
import com.inboyu.constant.DeleteFlag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 报修物品仓储实现
 * 实现报修物品的数据访问逻辑
 */
@Repository
public class RepairItemRepositoryImpl implements RepairItemRepository {

    @Autowired
    private RepairItemDao repairItemDao;

    @Autowired
    private RepairItemBuilder repairItemBuilder;

    /**
     * 定义kind字段的排序顺序
     */
    private static final List<String> KIND_ORDER = Arrays.asList(
            "电器", "水电路", "天地墙面", "卫浴", "门窗", "家具", "网络", "公共区域", "其它"
    );

    /**
     * 获取kind字段的排序权重
     *
     * @param kind 物品分类
     * @return 排序权重，越小越靠前
     */
    private int getKindOrder(String kind) {
        int index = KIND_ORDER.indexOf(kind);
        return index == -1 ? Integer.MAX_VALUE : index;
    }

    /**
     * 对报修物品列表进行排序：先按kind排序，再按sortOrder排序
     *
     * @param repairItems 报修物品列表
     * @return 排序后的报修物品列表
     */
    private List<RepairItem> sortRepairItems(List<RepairItem> repairItems) {
        return repairItems.stream()
                .sorted(Comparator
                        .comparingInt((RepairItem item) -> getKindOrder(item.getKind()))
                        .thenComparingInt(RepairItem::getSortOrder))
                .collect(Collectors.toList());
    }

    @Override
    public List<RepairItem> findAllActiveItems() {
        List<RepairItemEntity> entities = repairItemDao.findByIsActiveAndDeletedOrderBySortOrderAsc(
                true, DeleteFlag.NOT_DELETED);
        List<RepairItem> repairItems = entities.stream()
                .map(repairItemBuilder::toDomain)
                .collect(Collectors.toList());

        // 按kind和sortOrder排序后返回
        return sortRepairItems(repairItems);
    }

    @Override
    public List<RepairItem> findByIds(List<RepairItemId> itemIds) {
        if (itemIds == null || itemIds.isEmpty()) {
            return List.of();
        }

        List<Long> ids = itemIds.stream()
                .map(RepairItemId::getValue)
                .collect(Collectors.toList());

        List<RepairItemEntity> entities = repairItemDao.findByItemIdInAndDeleted(ids, DeleteFlag.NOT_DELETED);
        List<RepairItem> repairItems = entities.stream()
                .map(repairItemBuilder::toDomain)
                .collect(Collectors.toList());

        // 按kind和sortOrder排序后返回
        return sortRepairItems(repairItems);
    }

    @Override
    public RepairItem findById(RepairItemId itemId) {
        if (itemId == null) {
            return null;
        }

        RepairItemEntity entity = repairItemDao.findByItemIdAndDeleted(itemId.getValue(), DeleteFlag.NOT_DELETED);
        return repairItemBuilder.toDomain(entity);
    }

    @Override
    public List<RepairItem> findByKind(String kind) {
        if (kind == null || kind.trim().isEmpty()) {
            return List.of();
        }

        List<RepairItemEntity> entities = repairItemDao.findByKindAndDeletedOrderBySortOrderAsc(
                kind, DeleteFlag.NOT_DELETED);
        return entities.stream()
                .map(repairItemBuilder::toDomain)
                .collect(Collectors.toList());
    }
}
