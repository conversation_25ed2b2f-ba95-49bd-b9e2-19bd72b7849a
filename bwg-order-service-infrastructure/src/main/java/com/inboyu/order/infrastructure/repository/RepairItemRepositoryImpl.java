package com.inboyu.order.infrastructure.repository;

import com.inboyu.order.domain.repair.model.RepairItem;
import com.inboyu.order.domain.repair.model.RepairItemId;
import com.inboyu.order.domain.repair.repository.RepairItemRepository;
import com.inboyu.order.infrastructure.builder.RepairItemBuilder;
import com.inboyu.order.infrastructure.dao.RepairItemDao;
import com.inboyu.order.infrastructure.dao.entity.RepairItemEntity;
import com.inboyu.constant.DeleteFlag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 报修物品仓储实现
 * 实现报修物品的数据访问逻辑
 */
@Repository
public class RepairItemRepositoryImpl implements RepairItemRepository {

    @Autowired
    private RepairItemDao repairItemDao;

    @Autowired
    private RepairItemBuilder repairItemBuilder;

    @Override
    public List<RepairItem> findAllActiveItems() {
        List<RepairItemEntity> entities = repairItemDao.findByIsActiveAndDeletedOrderBySortOrderAsc(
                true, DeleteFlag.NOT_DELETED);
        return entities.stream()
                .map(repairItemBuilder::toDomain)
                .collect(Collectors.toList());
    }

    @Override
    public List<RepairItem> findByIds(List<RepairItemId> itemIds) {
        if (itemIds == null || itemIds.isEmpty()) {
            return List.of();
        }

        List<Long> ids = itemIds.stream()
                .map(RepairItemId::getValue)
                .collect(Collectors.toList());

        List<RepairItemEntity> entities = repairItemDao.findByItemIdInAndDeleted(ids, DeleteFlag.NOT_DELETED);
        return entities.stream()
                .map(repairItemBuilder::toDomain)
                .collect(Collectors.toList());
    }

    @Override
    public RepairItem findById(RepairItemId itemId) {
        if (itemId == null) {
            return null;
        }

        RepairItemEntity entity = repairItemDao.findByItemIdAndDeleted(itemId.getValue(), DeleteFlag.NOT_DELETED);
        return repairItemBuilder.toDomain(entity);
    }

    @Override
    public List<RepairItem> findByKind(String kind) {
        if (kind == null || kind.trim().isEmpty()) {
            return List.of();
        }

        List<RepairItemEntity> entities = repairItemDao.findByKindAndDeletedOrderBySortOrderAsc(
                kind, DeleteFlag.NOT_DELETED);
        return entities.stream()
                .map(repairItemBuilder::toDomain)
                .collect(Collectors.toList());
    }
}
