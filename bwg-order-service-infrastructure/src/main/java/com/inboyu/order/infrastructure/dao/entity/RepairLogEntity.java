package com.inboyu.order.infrastructure.dao.entity;

import com.inboyu.spring.cloud.starter.jpa.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Data;



@Entity
@Table(name = "t_repair_log")
@Data
public class RepairLogEntity extends BaseEntity {

    @Column(name = "log_id", nullable = false, unique = true)
    private Long logId; // 附件业务ID

    @Column(name = "log_key", nullable = false, length = 255)
    private String logKey; // 文件名

    @Column(name = "content", nullable = false, columnDefinition = "text")
    private String content; // 文件URL

}