package com.inboyu.order.infrastructure.workflow;

import com.alibaba.fastjson2.JSONObject;
import com.inboyu.order.domain.repair.service.WorkflowService;
import com.inboyu.admin.api.WorkflowFeignClient;
import com.inboyu.admin.dto.request.workflow.InstanceCreateRequestDTO;
import com.inboyu.admin.dto.request.workflow.InstanceApprovalRequestDTO;
import com.inboyu.admin.dto.request.workflow.InstanceCancelRequestDTO;
import com.inboyu.admin.dto.response.workflow.InstanceCreateResponseDTO;
import com.inboyu.admin.dto.response.workflow.InstanceStepBasicDTO;
import com.inboyu.admin.dto.response.workflow.InstanceCancelResponseDTO;
import com.inboyu.admin.dto.response.workflow.InstanceDTO;
import com.inboyu.spring.cloud.starter.http.api.container.FeignAccessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;

/**
 * 工作流服务实现
 * Infrastructure层实现，负责调用外部工作流API
 */
@Slf4j
@Service
public class WorkflowServiceImpl implements WorkflowService {

    @Autowired
    private FeignAccessor feignAccessor;

    @Override
    public String createRepairOrderWorkflow(String repairOrderId, String title, String description, 
                                          String userId, String userName, String storeId) {
        log.info("=== WorkflowServiceImpl.createRepairOrderWorkflow() 开始处理 ===");
        log.info("创建报修工单工作流，工单ID: {}, 标题: {}, 用户: {}, 门店ID: {}", repairOrderId, title, userName, storeId);
        
        try {
            // 获取工作流Feign客户端
            WorkflowFeignClient workflowClient = feignAccessor.get(WorkflowFeignClient.class);
            
            // 构建创建请求 - 按照测试代码中能正常工作的参数设置
            InstanceCreateRequestDTO request = new InstanceCreateRequestDTO();
            request.setForm("");
            
            // 必填字段
            request.setBusinessType("workflow_business_type.repair_request");  // 业务类型：报修业务类型
            request.setBusinessId(repairOrderId);                              // 业务唯一ID：报修工单ID
            request.setTitle(title);                                           // 工作流标题
            request.setUrl("https://manage.inboyu.com/repair/detail/" + repairOrderId);  // 单据网址：工单详情页URL
            
            // 用户信息（必填）
            request.setUserType("user_type.user");                             // 用户类型
            request.setUserId(userId);                                         // 用户ID
            request.setUserName(userName);                                     // 用户名
            
            // 可选字段
            request.setRemark(description);                                    // 工作流摘要：工单描述
            request.setStoreId(storeId);                                       // 门店ID
            
            // 设置初始参数
            HashMap<String, String> initParams = new HashMap<>();
            initParams.put("urgency", "");                               // 紧急程度
            request.setInitParams(initParams);
            
            // 打印请求参数详情
            log.info("=== 工作流创建请求参数 ===");
            log.info("业务类型: {}", request.getBusinessType());
            log.info("业务ID: {}", request.getBusinessId());
            log.info("标题: {}", request.getTitle());
            log.info("URL: {}", request.getUrl());
            log.info("用户类型: {}", request.getUserType());
            log.info("用户ID: {}", request.getUserId());
            log.info("用户名: {}", request.getUserName());
            log.info("备注: {}", request.getRemark());
            log.info("门店ID: {}", request.getStoreId());
            log.info("初始参数: {}", request.getInitParams());
            log.info("=== 请求参数结束 ===");
            
            // 调用admin-service创建工作流实例
            InstanceCreateResponseDTO response = workflowClient.create(request);
            
            // 详细打印响应信息
            log.info("=== 工作流创建响应详情 ===");
            log.info("报修工单工作流创建成功，工单ID: {}", repairOrderId);
            
            if (response != null) {
                log.info("工作流实例ID: {}", response.getWorkflowInstanceId());
                log.info("业务ID: {}", response.getBusinessId());
                log.info("完整响应对象: {}", response);
            } else {
                log.error("响应对象为 null！");
            }
            log.info("=== 响应详情结束 ===");
            
            if (response == null || response.getWorkflowInstanceId() == null) {
                throw new RuntimeException("工作流创建失败：响应为空或实例ID为空");
            }
            
            String workflowInstanceId = response.getWorkflowInstanceId();
            log.info("报修工单工作流创建成功，工单ID: {}, 工作流实例ID: {}", repairOrderId, workflowInstanceId);
            return workflowInstanceId;
            
        } catch (Exception e) {
            log.error("创建报修工单工作流失败，工单ID: {}, 错误信息: {}", repairOrderId, e.getMessage(), e);
            throw new RuntimeException("创建工作流失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String cancelWorkflow(String instanceId, String userName, String userType, String userId, String comment) {
        log.info("=== WorkflowServiceImpl.cancelWorkflow() 开始处理 ===");
        log.info("取消工作流，实例ID: {}, 用户: {}, 用户类型: {}", instanceId, userName, userType);
        
        try {
            // 获取工作流Feign客户端
            WorkflowFeignClient workflowClient = feignAccessor.get(WorkflowFeignClient.class);
            
            // 构建撤销请求
            InstanceCancelRequestDTO request = new InstanceCancelRequestDTO();
            request.setComment(comment);
            request.setUserType(userType);
            request.setUserId(userId);
            request.setUserName(userName);
            
            // 调用admin-service撤销工作流实例
            InstanceCancelResponseDTO response = workflowClient.cancel(instanceId, request);
            
            if (response == null) {
                throw new RuntimeException("工作流撤销失败：响应为空");
            }
            
            log.info("工作流撤销成功，实例ID: {}", instanceId);
            return "success";
            
        } catch (Exception e) {
            log.error("撤销工作流失败，实例ID: {}, 错误信息: {}", instanceId, e.getMessage(), e);
            throw new RuntimeException("撤销工作流失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String workflowApproval(String instanceStepId, String operateType, String comment,
                                 String userType, String userId, String userName) {
        log.info("=== WorkflowServiceImpl.workflowApproval() 开始处理 ===");
        log.info("工作流审批，实例步骤ID: {}, 操作类型: {}, 用户: {}, 用户类型: {}", instanceStepId, operateType, userName, userType);
        
        try {
            // 获取工作流Feign客户端
            WorkflowFeignClient workflowClient = feignAccessor.get(WorkflowFeignClient.class);
            
            // 构建审批请求
            InstanceApprovalRequestDTO request = new InstanceApprovalRequestDTO();
            request.setOperateType(operateType);
            request.setComment(comment);
            request.setUserType(userType);
            request.setUserId(userId);
            request.setUserName(userName);
            
            // 调用admin-service进行工作流审批
            InstanceStepBasicDTO response = workflowClient.approval(instanceStepId, request);
            
            if (response == null) {
                throw new RuntimeException("工作流审批失败：响应为空");
            }
            
            log.info("工作流审批成功，实例步骤ID: {}, 状态: {}", instanceStepId, response.getStatus());
            return "success";
            
        } catch (Exception e) {
            log.error("工作流审批失败，实例步骤ID: {}, 错误信息: {}", instanceStepId, e.getMessage(), e);
            throw new RuntimeException("工作流审批失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String getWorkflowInstance(String workflowInstanceId) {
        log.info("=== WorkflowServiceImpl.getWorkflowInstance() 开始处理 ===");
        log.info("查询工作流实例详情，实例ID: {}", workflowInstanceId);
        
        try {
            // 获取工作流Feign客户端
            WorkflowFeignClient workflowClient = feignAccessor.get(WorkflowFeignClient.class);
            
            // 调用admin-service查询工作流实例详情
            InstanceDTO response = workflowClient.getWorkflowInstance(workflowInstanceId);
            
            if (response == null) {
                log.warn("工作流实例不存在，实例ID: {}", workflowInstanceId);
                return "{}";
            }
            
            // 直接返回原始的工作流实例数据
            log.info("查询工作流实例详情成功，实例ID: {}", workflowInstanceId);
            return JSONObject.toJSONString(response);
            
        } catch (Exception e) {
            log.error("查询工作流实例详情失败，实例ID: {}, 错误信息: {}", workflowInstanceId, e.getMessage(), e);
            throw new RuntimeException("查询工作流实例失败: " + e.getMessage(), e);
        }
    }
}
