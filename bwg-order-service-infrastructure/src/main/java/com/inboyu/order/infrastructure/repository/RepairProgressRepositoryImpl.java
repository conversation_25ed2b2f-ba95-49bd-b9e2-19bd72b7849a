package com.inboyu.order.infrastructure.repository;

import com.inboyu.order.domain.repair.model.RepairProgress;
import com.inboyu.order.domain.repair.model.RepairProgressId;
import com.inboyu.order.domain.repair.model.RepairOrderId;
import com.inboyu.order.domain.repair.model.IsReopen;
import com.inboyu.order.domain.repair.model.OperatorId;
import com.inboyu.order.domain.repair.model.OperatorType;
import com.inboyu.order.domain.repair.model.ProgressStatus;
import com.inboyu.order.domain.repair.repository.RepairProgressRepository;
import com.inboyu.order.infrastructure.dao.entity.RepairProgressEntity;
import com.inboyu.order.infrastructure.dao.RepairProgressDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 报修进度仓储实现
 * 实现报修进度的数据访问逻辑
 */
@Slf4j
@Repository
public class RepairProgressRepositoryImpl implements RepairProgressRepository {

    @Autowired
    private RepairProgressDao repairProgressDao;

    @Override
    public RepairProgressId generateProgressId() {
        // 生成进度ID的逻辑，这里使用简单的递增ID
        // 实际项目中可能需要使用雪花算法或其他分布式ID生成策略
        long id = System.currentTimeMillis();
        return RepairProgressId.of(id);
    }

    @Override
    public RepairProgress save(RepairProgress repairProgress) {
        log.info("保存报修进度，进度ID：{}", repairProgress.getProgressId().getValue());
        
        RepairProgressEntity entity = convertToEntity(repairProgress);
        repairProgressDao.save(entity);
        
        log.info("保存报修进度成功，进度ID：{}", repairProgress.getProgressId().getValue());
        return repairProgress;
    }


    @Override
    public List<RepairProgress> findByRepairOrderIdOrderByCreateTimeDesc(RepairOrderId orderId) {
        log.info("根据订单ID查询报修进度列表（按创建时间倒序），订单ID：{}", orderId.getValue());
        
        List<RepairProgressEntity> entities = repairProgressDao.findByRepairOrderIdAndDeletedOrderByCreateTimeAsc(orderId.getValue(), 0);
        List<RepairProgress> progressList = entities.stream()
                .map(this::convertToDomain)
                .collect(Collectors.toList());
        
        log.info("查询报修进度列表成功，订单ID：{}，数量：{}", orderId.getValue(), progressList.size());
        return progressList;
    }


    /**
     * 将领域对象转换为实体对象
     *
     * @param repairProgress 领域对象
     * @return 实体对象
     */
    private RepairProgressEntity convertToEntity(RepairProgress repairProgress) {
        RepairProgressEntity entity = new RepairProgressEntity();
        entity.setProgressId(repairProgress.getProgressId().getValue());
        entity.setRepairOrderId(repairProgress.getRepairOrderId().getValue());
        entity.setOperatorId(repairProgress.getOperatorId().getValue());
        entity.setOperatorName(repairProgress.getOperatorName());
        entity.setDetail(repairProgress.getDetail());
        entity.setSubDetail(repairProgress.getSubDetail());
        entity.setOperatorType(repairProgress.getOperatorType().getValue());
        entity.setStatus(repairProgress.getStatus().getValue());
        entity.setComment(repairProgress.getComment());
        entity.setIsReopen(repairProgress.getIsReopen().getValue());
        entity.setReopenReason(repairProgress.getReopenReason());
        return entity;
    }

    /**
     * 将实体对象转换为领域对象
     *
     * @param entity 实体对象
     * @return 领域对象
     */
    private RepairProgress convertToDomain(RepairProgressEntity entity) {
        return RepairProgress.builder()
                .progressId(RepairProgressId.of(entity.getProgressId()))
                .repairOrderId(RepairOrderId.of(entity.getRepairOrderId()))
                .operatorId(OperatorId.of(entity.getOperatorId()))
                .operatorName(entity.getOperatorName())
                .detail(entity.getDetail())
                .subDetail(entity.getSubDetail())
                .operatorType(OperatorType.of(entity.getOperatorType()))
                .status(ProgressStatus.of(entity.getStatus()))
                .comment(entity.getComment())
                .isReopen(IsReopen.of(entity.getIsReopen()))
                .reopenReason(entity.getReopenReason())
                .createTime(entity.getCreateTime())
                .modifyTime(entity.getModifyTime())
                .build();
    }
}
