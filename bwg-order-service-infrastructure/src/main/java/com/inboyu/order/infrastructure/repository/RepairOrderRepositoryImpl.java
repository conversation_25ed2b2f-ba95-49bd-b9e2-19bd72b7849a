package com.inboyu.order.infrastructure.repository;

import com.inboyu.order.domain.repair.model.*;
import com.inboyu.order.domain.repair.repository.RepairOrderRepository;
import com.inboyu.order.infrastructure.builder.RepairAttachmentBuilder;
import com.inboyu.order.infrastructure.builder.RepairProgressBuilder;
import com.inboyu.order.infrastructure.builder.impl.RepairOrderBuilderImpl;
import com.inboyu.order.infrastructure.dao.RepairAttachmentDao;
import com.inboyu.order.infrastructure.dao.RepairOrderDao;
import com.inboyu.order.infrastructure.dao.RepairProgressDao;
import com.inboyu.order.infrastructure.dao.entity.RepairAttachmentEntity;
import com.inboyu.order.infrastructure.dao.entity.RepairOrderEntity;
import com.inboyu.order.infrastructure.dao.entity.RepairProgressEntity;
import com.inboyu.spring.cloud.starter.common.dto.Pagination;
import com.inboyu.spring.cloud.starter.redis.utils.id.RedisSnowflakeIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 报修订单仓储实现
 */
@Slf4j
@Repository
public class RepairOrderRepositoryImpl implements RepairOrderRepository {

    @Autowired
    private RepairOrderDao repairOrderDao;

    @Autowired
    private RepairAttachmentDao repairAttachmentDao;

    @Autowired
    private RepairProgressDao repairProgressDao;

    @Autowired
    private RepairOrderBuilderImpl repairOrderBuilder;

    @Autowired
    private RedisSnowflakeIdGenerator snowflakeIdGenerator;

    @Autowired
    private RepairAttachmentBuilder repairAttachmentBuilder;

    @Autowired
    private RepairProgressBuilder repairProgressBuilder;

    @Override
    public RepairOrderId generateId() {
        Long id = snowflakeIdGenerator.nextId();
        return RepairOrderId.of(id);
    }

    @Override
    public AttachmentId generateAttachmentId() {
        Long id = snowflakeIdGenerator.nextId();
        return AttachmentId.of(id);
    }

    @Override
    public RepairProgressId generateProgressId() {
        Long id = snowflakeIdGenerator.nextId();
        return RepairProgressId.of(id);
    }

    @Override
    public RepairOrder save(RepairOrder repairOrder) {
        log.info("保存报修订单，订单ID：{}", repairOrder.getRepairOrderId());

        // 转换为数据库实体
        RepairOrderEntity orderEntity = repairOrderBuilder.toEntity(repairOrder);

        // 保存报修订单
        RepairOrderEntity savedOrderEntity = repairOrderDao.save(orderEntity);
        log.info("成功保存报修订单实体，数据库ID：{}", savedOrderEntity.getId());

        // 保存附件
        if (repairOrder.getAttachments() != null && !repairOrder.getAttachments().isEmpty()) {
            List<RepairAttachmentEntity> attachmentEntities = repairOrder.getAttachments().stream()
                    .map(attachment -> repairAttachmentBuilder.toEntity(attachment, savedOrderEntity.getRepairOrderId()))
                    .collect(Collectors.toList());

            List<RepairAttachmentEntity> savedAttachments = repairAttachmentDao.saveAll(attachmentEntities);
            log.info("成功保存报修附件，数量：{}", savedAttachments.size());
        }

        // 重新构建领域对象返回
        return repairOrderBuilder.toDomain(savedOrderEntity, loadAttachments(savedOrderEntity.getRepairOrderId()));
    }

    @Override
    public List<RepairOrder> batchSave(List<RepairOrder> repairOrders) {
        log.info("批量保存报修订单，数量：{}", repairOrders.size());

        return repairOrders.stream()
                .map(this::save)
                .collect(Collectors.toList());
    }

    @Override
    public RepairOrder findByRepairOrderId(RepairOrderId orderId) {
        log.info("查询报修订单，订单ID：{}", orderId);

        Optional<RepairOrderEntity> orderEntityOpt = repairOrderDao.findByRepairOrderIdAndDeleted(orderId.getValue(), 0);
        if (orderEntityOpt.isEmpty()) {
            log.warn("报修订单不存在，订单ID：{}", orderId);
            return null;
        }

        RepairOrderEntity orderEntity = orderEntityOpt.get();
        List<RepairAttachmentEntity> attachmentEntities = loadAttachments(orderEntity.getRepairOrderId());

        return repairOrderBuilder.toDomain(orderEntity, attachmentEntities);
    }

    /**
     * 加载附件
     */
    private List<RepairAttachmentEntity> loadAttachments(Long repairOrderId) {
        return repairAttachmentDao.findByRepairOrderIdAndDeleted(repairOrderId, 0);
    }

    @Override
    public RepairProgress saveProgress(RepairProgress repairProgress) {
        log.info("保存报修进度，进度ID：{}", repairProgress.getProgressId());

        // 转换为数据库实体
        RepairProgressEntity progressEntity = repairProgressBuilder.toEntity(repairProgress);

        // 保存报修进度
        RepairProgressEntity savedProgressEntity = repairProgressDao.save(progressEntity);
        log.info("成功保存报修进度实体，数据库ID：{}", savedProgressEntity.getId());

        // 重新构建领域对象返回
        return repairProgressBuilder.toDomain(savedProgressEntity);
    }

    @Override
    public List<RepairProgress> findProgressListByRepairOrderId(RepairOrderId orderId) {
        log.info("查询报修订单进度列表，订单ID：{}", orderId);

        // 查询进度实体列表
        List<RepairProgressEntity> progressEntities = repairProgressDao.findByRepairOrderIdAndDeletedOrderByCreateTimeAsc(orderId.getValue(), 0);
        log.info("查询到报修进度数量：{}", progressEntities.size());

        // 转换为领域对象列表
        return progressEntities.stream()
                .map(repairProgressBuilder::toDomain)
                .collect(Collectors.toList());
    }

    @Override
    public Pagination<RepairOrder> pageRepairOrder(
            Integer pageNum, Integer pageSize,
            Long customerId, Long storeId, List<String> statusList,
            Long responsibleId, Long roomId, String roomNum) {
        log.info("小程序端分页查询报修订单，pageNum：{}，pageSize：{}，customerId：{}，storeId：{}，statusList：{}，responsibleId：{}，roomId：{}，roomNum：{}",
                pageNum, pageSize, customerId, storeId, statusList, responsibleId, roomId, roomNum);

        // 构建分页参数
        Pageable pageable = PageRequest.of(pageNum - 1, pageSize);

        // 分页查询订单实体
        Page<RepairOrderEntity> entityPage = repairOrderDao.pageByFilters(
                customerId, storeId, statusList, responsibleId, roomId, roomNum, pageable);

        // 转换为领域对象
        List<RepairOrder> repairOrders = entityPage.getContent().stream()
                .map(entity -> {
                    List<RepairAttachmentEntity> attachments = loadAttachments(entity.getRepairOrderId());
                    return repairOrderBuilder.toDomain(entity, attachments);
                })
                .collect(Collectors.toList());

        return new Pagination<>(
                pageNum,
                pageSize,
                entityPage.getTotalPages(),
                entityPage.getTotalElements(),
                repairOrders
        );
    }
}

