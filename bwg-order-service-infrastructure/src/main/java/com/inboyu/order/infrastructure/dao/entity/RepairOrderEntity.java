package com.inboyu.order.infrastructure.dao.entity;

import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 报修订单数据库实体
 */
@Data
@Entity
@Table(name = "t_repair_order")
public class RepairOrderEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "repair_order_id", nullable = false, unique = true)
    private Long repairOrderId;

    @Column(name = "customer_id", nullable = false)
    private Long customerId;

    @Column(name = "store_id", nullable = false)
    private Long storeId;

    @Column(name = "room_id", nullable = false)
    private Long roomId;

    @Column(name = "building_id", nullable = false)
    private Long buildingId;

    @Column(name = "item_id", nullable = false)
    private Long itemId;

    @Column(name = "item_tag", nullable = false)
    private String itemTag;

    @Column(name = "workflow_id", nullable = false)
    private String workflowId;

    @Column(name = "room_number", nullable = false)
    private String roomNumber;

    @Column(name = "area", nullable = false)
    private String area;

    @Column(name = "detail", nullable = false)
    private String detail;

    @Column(name = "contact_phone", nullable = false)
    private String contactPhone;

    @Column(name = "submitter", nullable = false)
    private String submitter;

    @Column(name = "will_repair_date")
    private LocalDate willRepairDate;

    @Column(name = "will_repair_start")
    private LocalTime willRepairStart;

    @Column(name = "will_repair_end")
    private LocalTime willRepairEnd;

    @Column(name = "is_enter")
    private Integer isEnter;

    @Column(name = "status", nullable = false)
    private String status;

    @Column(name = "order_type", nullable = false)
    private String orderType;

    @Column(name = "tag_status", nullable = false)
    private String tagStatus;

    @Column(name = "refuse_reason")
    private String refuseReason;

    @Column(name = "suspend_reason")
    private String suspendReason;

    @Column(name = "pre_door_time")
    private LocalDateTime preDoorTime;

    @Column(name = "pre_repair_time")
    private LocalDateTime preRepairTime;

    @Column(name = "finish_time")
    private LocalDateTime finishTime;

    @Column(name = "deal_time")
    private LocalDateTime dealTime;

    @Column(name = "responsible_id")
    private Long responsibleId;

    @Column(name = "maintainer_id")
    private Long maintainerId;

    @Column(name = "remark")
    private String remark;

    @CreationTimestamp
    @Column(name = "create_time", nullable = false, updatable = false)
    private LocalDateTime createTime;

    @UpdateTimestamp
    @Column(name = "modify_time", nullable = false)
    private LocalDateTime modifyTime;

    @Version
    @Column(name = "version", nullable = false)
    private Long version;

    @Column(name = "deleted", nullable = false)
    private Integer deleted = 0;
}
