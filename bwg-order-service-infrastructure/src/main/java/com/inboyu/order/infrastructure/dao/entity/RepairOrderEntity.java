package com.inboyu.order.infrastructure.dao.entity;

import com.inboyu.spring.cloud.starter.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 报修工单实体类
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "t_repair_order")
public class RepairOrderEntity extends BaseEntity {

    @Column(name = "repair_order_id", nullable = false)
    private Long repairOrderId;

    @Column(name = "customer_id", nullable = false)
    private Long customerId;

    @Column(name = "store_id", nullable = false)
    private Long storeId;

    @Column(name = "room_id", nullable = false)
    private Long roomId;

    @Column(name = "building_id", nullable = false)
    private Long buildingId;

    @Column(name = "item_id", nullable = false)
    private Long itemId;

    @Column(name = "workflow_id", nullable = false)
    private Long workflowId;

    @Column(name = "room_number", nullable = false, length = 50)
    private String roomNumber;

    @Column(name = "area", nullable = false, length = 100)
    private String area;

    @Column(name = "detail", nullable = false, length = 500)
    private String detail;

    @Column(name = "contact_phone", nullable = false, length = 500)
    private String contactPhone;

    @Column(name = "submitter", nullable = false, length = 50)
    private String submitter;

    @Column(name = "will_repair_date")
    private LocalDate willRepairDate;

    @Column(name = "will_repair_start", nullable = false)
    private Integer willRepairStart;

    @Column(name = "will_repair_end", nullable = false)
    private Integer willRepairEnd;

    @Column(name = "is_enter", nullable = false)
    private Boolean isEnter;

    @Column(name = "status", nullable = false, length = 200)
    private String status;

    @Column(name = "order_type", nullable = false, length = 200)
    private String orderType;

    @Column(name = "tag_status", nullable = false, length = 200)
    private String tagStatus;

    @Column(name = "refuse_reason", nullable = false, length = 200)
    private String refuseReason;

    @Column(name = "suspend_reason", nullable = false, length = 200)
    private String suspendReason;

    @Column(name = "pre_door_time")
    private LocalDateTime preDoorTime;

    @Column(name = "pre_repair_time")
    private LocalDateTime preRepairTime;

    @Column(name = "finish_time")
    private LocalDateTime finishTime;

    @Column(name = "deal_time")
    private LocalDateTime dealTime;

    @Column(name = "responsible_id", nullable = false)
    private Long responsibleId;

    @Column(name = "maintainer_id", nullable = false)
    private Long maintainerId;

    @Column(name = "remark", nullable = false, length = 500)
    private String remark;
}
