package com.inboyu.order.infrastructure.builder.impl;

import com.inboyu.order.domain.repair.model.*;
import com.inboyu.order.infrastructure.builder.RepairProgressBuilder;
import com.inboyu.order.infrastructure.dao.entity.RepairProgressEntity;
import org.springframework.stereotype.Component;

@Component
public class RepairProgressBuilderImpl implements RepairProgressBuilder {

    /**
     * 将报修进度领域对象转换为数据库实体
     */
    public RepairProgressEntity toEntity(RepairProgress progress) {
        RepairProgressEntity entity = new RepairProgressEntity();

        entity.setProgressId(progress.getProgressId().getValue());
        entity.setRepairOrderId(progress.getRepairOrderId().getValue());
        entity.setOperatorId(progress.getOperatorId().getValue());
        entity.setOperatorName(progress.getOperatorName());
        entity.setDetail(progress.getDetail());
        entity.setSubDetail(progress.getSubDetail());
        entity.setOperatorType(progress.getOperatorType().getCode());
        entity.setStatus(progress.getStatus().getCode());
        entity.setComment(progress.getComment());
        entity.setIsReopen(progress.getIsReopen().getValue());
        entity.setReopenReason(progress.getReopenReason());

        return entity;
    }

    /**
     * 将报修进度数据库实体转换为领域对象
     */
    public RepairProgress toDomain(RepairProgressEntity entity) {
        return RepairProgress.builder()
                .progressId(RepairProgressId.of(entity.getProgressId()))
                .repairOrderId(RepairOrderId.of(entity.getRepairOrderId()))
                .operatorId(OperatorId.of(entity.getOperatorId()))
                .operatorName(entity.getOperatorName())
                .detail(entity.getDetail())
                .subDetail(entity.getSubDetail())
                .operatorType(OperatorType.of(entity.getOperatorType()))
                .status(ProgressStatus.of(entity.getStatus()))
                .comment(entity.getComment())
                .isReopen(IsReopen.of(entity.getIsReopen()))
                .reopenReason(entity.getReopenReason())
                .build();
    }
}
