package com.inboyu.order.infrastructure.dao.entity;

import com.inboyu.spring.cloud.starter.jpa.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * 门店报修物品配置实体类
 * 对应数据库表：t_repair_store_item
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_repair_store_item")
@DynamicInsert
@DynamicUpdate
public class RepairStoreItemEntity extends BaseEntity {

    @Column(name = "config_id", nullable = false)
    private Long configId; // 配置业务ID

    @Column(name = "store_id", nullable = false)
    private Long storeId; // 门店ID

    @Column(name = "item_id", nullable = false)
    private Long itemId; // 物品ID
}
