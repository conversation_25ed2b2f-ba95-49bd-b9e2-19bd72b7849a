package com.inboyu.order.infrastructure.dao;

import com.inboyu.order.infrastructure.dao.entity.RepairAttachmentEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 报修附件数据访问接口
 */
@Repository
public interface RepairAttachmentDao extends JpaRepository<RepairAttachmentEntity, Long> {

    /**
     * 根据附件ID和删除标志查询报修附件
     *
     * @param attachmentId 附件ID
     * @param deleted      删除标志
     * @return 报修附件实体
     */
    Optional<RepairAttachmentEntity> findByAttachmentIdAndDeleted(Long attachmentId, Integer deleted);

    /**
     * 根据报修订单ID查询附件列表
     *
     * @param repairOrderId 报修订单ID
     * @param deleted        删除标识
     * @return 附件列表
     */
    List<RepairAttachmentEntity> findByRepairOrderIdAndDeleted(Long repairOrderId, Integer deleted);

    /**
     * 更新报修附件
     *
     * @param entity 报修附件实体
     */
    @Modifying
    @Query("UPDATE RepairAttachmentEntity e SET " +
           "e.fileName = :#{#entity.fileName}, " +
           "e.fileUrl = :#{#entity.fileUrl}, " +
           "e.fileType = :#{#entity.fileType}, " +
           "e.attachmentType = :#{#entity.attachmentType}, " +
           "e.fileSize = :#{#entity.fileSize}, " +
           "e.uploadTime = :#{#entity.uploadTime}, " +
           "e.modifyTime = CURRENT_TIMESTAMP, " +
           "e.version = e.version + 1 " +
           "WHERE e.attachmentId = :#{#entity.attachmentId} AND e.deleted = 0")
    void update(@Param("entity") RepairAttachmentEntity entity);

    /**
     * 批量保存附件
     *
     * @param entities 附件实体列表
     * @return 保存后的附件实体列表
     */
    @Override
    <S extends RepairAttachmentEntity> List<S> saveAll(Iterable<S> entities);
}
