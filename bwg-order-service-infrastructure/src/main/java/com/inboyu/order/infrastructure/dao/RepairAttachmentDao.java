package com.inboyu.order.infrastructure.dao;

import com.inboyu.order.infrastructure.dao.entity.RepairAttachmentEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 报修附件数据访问接口
 */
@Repository
public interface RepairAttachmentDao extends JpaRepository<RepairAttachmentEntity, Long> {

    /**
     * 根据附件ID和删除标志查询报修附件
     *
     * @param attachmentId 附件ID
     * @param deleted      删除标志
     * @return 报修附件实体
     */
    RepairAttachmentEntity findByAttachmentIdAndDeleted(Long attachmentId, Integer deleted);

    /**
     * 更新报修附件
     *
     * @param entity 报修附件实体
     */
    @Modifying
    @Query("UPDATE RepairAttachmentEntity e SET " +
           "e.fileName = :#{#entity.fileName}, " +
           "e.fileUrl = :#{#entity.fileUrl}, " +
           "e.fileType = :#{#entity.fileType}, " +
           "e.attachmentType = :#{#entity.attachmentType}, " +
           "e.fileSize = :#{#entity.fileSize}, " +
           "e.uploadTime = :#{#entity.uploadTime}, " +
           "e.modifyTime = CURRENT_TIMESTAMP, " +
           "e.version = e.version + 1 " +
           "WHERE e.attachmentId = :#{#entity.attachmentId} AND e.deleted = 0")
    void update(@Param("entity") RepairAttachmentEntity entity);
}
