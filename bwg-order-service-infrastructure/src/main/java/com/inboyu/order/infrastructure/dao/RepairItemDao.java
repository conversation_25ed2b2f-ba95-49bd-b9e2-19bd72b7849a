package com.inboyu.order.infrastructure.dao;

import com.inboyu.order.infrastructure.dao.entity.RepairItemEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 报修物品数据访问接口
 */
@Repository
public interface RepairItemDao extends JpaRepository<RepairItemEntity, Long> {

    /**
     * 查询所有启用的报修物品，按排序字段排序
     *
     * @param isActive 是否启用
     * @param deleted  删除标志
     * @return 报修物品实体列表
     */
    List<RepairItemEntity> findByIsActiveAndDeletedOrderBySortOrderAsc(Boolean isActive, Integer deleted);

    /**
     * 根据物品ID列表查询报修物品
     *
     * @param itemIds 物品ID列表
     * @param deleted 删除标志
     * @return 报修物品实体列表
     */
    List<RepairItemEntity> findByItemIdInAndDeleted(List<Long> itemIds, Integer deleted);

    /**
     * 根据物品ID查询报修物品
     *
     * @param itemId  物品ID
     * @param deleted 删除标志
     * @return 报修物品实体
     */
    RepairItemEntity findByItemIdAndDeleted(Long itemId, Integer deleted);

    /**
     * 根据分类查询报修物品
     *
     * @param kind    物品分类
     * @param deleted 删除标志
     * @return 报修物品实体列表
     */
    List<RepairItemEntity> findByKindAndDeletedOrderBySortOrderAsc(String kind, Integer deleted);
}
