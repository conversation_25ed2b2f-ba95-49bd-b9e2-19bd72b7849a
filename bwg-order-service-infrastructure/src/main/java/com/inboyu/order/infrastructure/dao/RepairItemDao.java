package com.inboyu.order.infrastructure.dao;

import com.inboyu.order.infrastructure.dao.entity.RepairItemEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 报修物品数据访问接口
 */
@Repository
public interface RepairItemDao extends JpaRepository<RepairItemEntity, Long> {

    /**
     * 查询所有启用的报修物品，按排序字段排序
     *
     * @param isActive 是否启用
     * @param deleted  删除标志
     * @return 报修物品实体列表
     */
    List<RepairItemEntity> findByIsActiveAndDeletedOrderBySortOrderAsc(Boolean isActive, Integer deleted);

    /**
     * 根据物品ID列表查询报修物品
     *
     * @param itemIds 物品ID列表
     * @param deleted 删除标志
     * @return 报修物品实体列表
     */
    List<RepairItemEntity> findByItemIdInAndDeleted(List<Long> itemIds, Integer deleted);

    /**
     * 根据门店ID和物品名称模糊搜索门店可报修物品
     *
     * @param storeId 门店ID
     * @param keyword 搜索关键词
     * @param deleted 删除标志
     * @return 报修物品实体列表
     */
    @Query("SELECT ri FROM RepairItemEntity ri " +
           "WHERE ri.itemId IN (" +
           "    SELECT rsi.itemId FROM RepairStoreItemEntity rsi " +
           "    WHERE rsi.storeId = :storeId AND rsi.deleted = :deleted" +
           ") " +
           "AND ri.name LIKE %:keyword% " +
           "AND ri.isActive = true " +
           "AND ri.deleted = :deleted " +
           "ORDER BY ri.sortOrder ASC")
    List<RepairItemEntity> findStoreRepairItemsByNameContaining(@Param("storeId") Long storeId,
                                                               @Param("keyword") String keyword,
                                                               @Param("deleted") Integer deleted);
}
