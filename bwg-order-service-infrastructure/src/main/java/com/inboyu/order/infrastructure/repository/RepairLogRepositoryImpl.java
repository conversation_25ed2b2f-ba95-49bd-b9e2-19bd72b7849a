package com.inboyu.order.infrastructure.repository;

import com.inboyu.order.domain.repair.repository.RepairLogRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

/**
 * 报修日志仓储实现
 */
@Slf4j
@Repository
public class RepairLogRepositoryImpl implements RepairLogRepository {

    @Override
    public void save(String key, String content) {
        // 这里可以实现具体的日志保存逻辑
        // 比如保存到数据库、文件系统或者发送到日志系统
        log.info("保存报修日志: key={}, content={}", key, content);
    }
}
