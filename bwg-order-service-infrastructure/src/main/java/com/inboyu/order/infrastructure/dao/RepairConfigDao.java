package com.inboyu.order.infrastructure.dao;

import com.inboyu.order.infrastructure.dao.entity.RepairConfigEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 报修配置数据访问接口
 */
@Repository
public interface RepairConfigDao extends JpaRepository<RepairConfigEntity, Long> {

    /**
     * 根据配置ID和删除标志查询报修配置
     *
     * @param configId 配置ID
     * @param deleted  删除标志
     * @return 报修配置实体
     */
    RepairConfigEntity findByConfigIdAndDeleted(Long configId, Integer deleted);

    /**
     * 根据门店ID和删除标志查询报修配置
     *
     * @param storeId 门店ID
     * @param deleted 删除标志
     * @return 报修配置实体
     */
    RepairConfigEntity findByStoreIdAndDeleted(Long storeId, Integer deleted);

    /**
     * 更新报修配置
     *
     * @param entity 报修配置实体
     */
    @Modifying
    @Query("UPDATE RepairConfigEntity e SET " +
           "e.repairStartTime = :#{#entity.repairStartTime}, " +
           "e.repairEndTime = :#{#entity.repairEndTime}, " +
           "e.repairTimeInterval = :#{#entity.repairTimeInterval}, " +
           "e.repairWeekDay = :#{#entity.repairWeekDay}, " +
           "e.maxRepairDays = :#{#entity.maxRepairDays}, " +
           "e.repairTodayEnable = :#{#entity.repairTodayEnable}, " +
           "e.modifyTime = CURRENT_TIMESTAMP, " +
           "e.version = e.version + 1 " +
           "WHERE e.configId = :#{#entity.configId} AND e.deleted = 0")
    void update(@Param("entity") RepairConfigEntity entity);
}
