package com.inboyu.order.infrastructure.builder.impl;

import com.inboyu.order.domain.repair.model.RepairAttachment;
import com.inboyu.order.domain.repair.model.RepairOrderId;
import com.inboyu.order.domain.repair.model.AttachmentType;
import com.inboyu.order.infrastructure.entity.RepairAttachmentEntity;
import com.inboyu.order.infrastructure.builder.RepairAttachmentBuilder;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 报修附件Builder实现
 */
@Component
public class RepairAttachmentBuilderImpl implements RepairAttachmentBuilder {

    @Override
    public RepairAttachmentEntity toEntity(RepairAttachment domainModel) {
        if (domainModel == null) {
            return null;
        }

        RepairAttachmentEntity entity = new RepairAttachmentEntity();
        entity.setAttachmentId(domainModel.getAttachmentId());
        entity.setRepairOrderId(domainModel.getRepairOrderId().getValue());
        entity.setFileName(domainModel.getFileName());
        entity.setFileUrl(domainModel.getFileUrl());
        entity.setFileType(domainModel.getFileType());
        entity.setAttachmentType(domainModel.getAttachmentType().getCode());
        entity.setFileSize(domainModel.getFileSize());
        entity.setUploadTime(domainModel.getUploadTime());
        entity.setCreateTime(domainModel.getCreateTime());
        entity.setModifyTime(domainModel.getModifyTime());
        entity.setVersion(domainModel.getVersion().intValue());
        entity.setDeleted(domainModel.getDeleted());

        return entity;
    }

    @Override
    public RepairAttachment toDomain(RepairAttachmentEntity entity) {
        if (entity == null) {
            return null;
        }

        return RepairAttachment.builder()
                .attachmentId(entity.getAttachmentId())
                .repairOrderId(RepairOrderId.of(entity.getRepairOrderId()))
                .fileName(entity.getFileName())
                .fileUrl(entity.getFileUrl())
                .fileType(entity.getFileType())
                .attachmentType(AttachmentType.of(entity.getAttachmentType()))
                .fileSize(entity.getFileSize())
                .uploadTime(entity.getUploadTime())
                .createTime(entity.getCreateTime())
                .modifyTime(entity.getModifyTime())
                .version(Long.valueOf(entity.getVersion()))
                .deleted(entity.getDeleted())
                .build();
    }


    @Override
    public List<RepairAttachment> toDomains(List<RepairAttachmentEntity> entities) {
        if (entities == null) {
            return null;
        }
        return entities.stream()
                .map(this::toDomain)
                .collect(Collectors.toList());
    }

    @Override
    public List<RepairAttachmentEntity> toEntities(List<RepairAttachment> domains) {
        if (domains == null) {
            return null;
        }
        return domains.stream()
                .map(this::toEntity)
                .collect(Collectors.toList());
    }
}
