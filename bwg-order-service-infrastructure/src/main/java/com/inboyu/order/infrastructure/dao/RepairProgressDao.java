package com.inboyu.order.infrastructure.dao;

import com.google.common.io.Files;
import com.inboyu.order.infrastructure.dao.entity.RepairProgressEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 报修进度DAO
 */
@Repository
public interface RepairProgressDao extends JpaRepository<RepairProgressEntity, Long> {

    /**
     * 根据进度ID查询
     *
     * @param progressId 进度ID
     * @param deleted    删除标识
     * @return 进度实体
     */
    Optional<RepairProgressEntity> findByProgressIdAndDeleted(Long progressId, Integer deleted);

    /**
     * 根据报修订单ID查询进度列表
     *
     * @param repairOrderId 报修订单ID
     * @param deleted       删除标识
     * @return 进度列表
     */
    List<RepairProgressEntity> findByRepairOrderIdAndDeletedOrderByCreateTimeAsc(Long repairOrderId, Integer deleted);

    /**
     * 根据repairOrderId 查询最新的一条记录
     *
     * @param repairOrderId
     * @param deleted
     * @return
     */
    Optional<RepairProgressEntity> findTopByRepairOrderIdAndDeletedOrderByCreateTimeDesc(Long repairOrderId, Integer deleted);

}
