package com.inboyu.order.infrastructure.dao;

import com.inboyu.order.infrastructure.dao.entity.RepairProgressEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 报修进度数据访问接口
 */
@Repository
public interface RepairProgressDao extends JpaRepository<RepairProgressEntity, Long> {

    /**
     * 根据进度ID和删除标志查询报修进度
     *
     * @param progressId 进度ID
     * @param deleted    删除标志
     * @return 报修进度实体
     */
    RepairProgressEntity findByProgressIdAndDeleted(Long progressId, Integer deleted);

    /**
     * 根据报修工单ID和删除标志查询报修进度列表
     *
     * @param repairOrderId 报修工单ID
     * @param deleted       删除标志
     * @return 报修进度列表
     */
    List<RepairProgressEntity> findByRepairOrderIdAndDeleted(Long repairOrderId, Integer deleted);

    /**
     * 根据用户ID和删除标志查询报修进度列表
     *
     * @param userId  用户ID
     * @param deleted 删除标志
     * @return 报修进度列表
     */
    List<RepairProgressEntity> findByUserIdAndDeleted(Long userId, Integer deleted);

    /**
     * 更新报修进度
     *
     * @param entity 报修进度实体
     */
    @Modifying
    @Query("UPDATE RepairProgressEntity e SET " +
           "e.repairOrderId = :#{#entity.repairOrderId}, " +
           "e.userId = :#{#entity.userId}, " +
           "e.userName = :#{#entity.userName}, " +
           "e.detail = :#{#entity.detail}, " +
           "e.type = :#{#entity.type}, " +
           "e.status = :#{#entity.status}, " +
           "e.comment = :#{#entity.comment}, " +
           "e.distributeReason = :#{#entity.distributeReason}, " +
           "e.distributeDetail = :#{#entity.distributeDetail}, " +
           "e.isReopen = :#{#entity.isReopen}, " +
           "e.reopenReason = :#{#entity.reopenReason}, " +
           "e.processMethod = :#{#entity.processMethod}, " +
           "e.assigneeId = :#{#entity.assigneeId}, " +
           "e.assigneeName = :#{#entity.assigneeName}, " +
           "e.modifyTime = CURRENT_TIMESTAMP, " +
           "e.version = e.version + 1 " +
           "WHERE e.progressId = :#{#entity.progressId} AND e.deleted = 0")
    void update(@Param("entity") RepairProgressEntity entity);
}
