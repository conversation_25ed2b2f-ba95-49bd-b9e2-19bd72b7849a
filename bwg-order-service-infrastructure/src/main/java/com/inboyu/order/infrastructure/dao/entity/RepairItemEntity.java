package com.inboyu.order.infrastructure.dao.entity;

import com.inboyu.spring.cloud.starter.jpa.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


/**
 * <AUTHOR>
 * @date 2025/9/15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "t_repair_item")
@DynamicInsert
@DynamicUpdate
public class RepairItemEntity extends BaseEntity {

    @Column(name = "item_id", nullable = false)
    private Long itemId; // 物品业务ID

    @Column(name = "kind", nullable = false)
    private String kind; // 物品分类

    @Column(name = "name", nullable = false)
    private String name; // 物品名称

    @Column(name = "icon", nullable = false)
    private String icon; // 物品图标

    @Column(name = "url", nullable = false)
    private String url; // Logo地址

    @Column(name = "remark", nullable = false)
    private String remark; // 故障描述标签

    @Column(name = "sort_order", nullable = false)
    private Integer sortOrder; // 排序

    @Column(name = "is_active", nullable = false)
    private Boolean isActive; // 是否启用
}
