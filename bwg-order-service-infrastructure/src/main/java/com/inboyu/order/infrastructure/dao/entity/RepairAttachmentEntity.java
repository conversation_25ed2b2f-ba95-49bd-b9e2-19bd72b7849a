package com.inboyu.order.infrastructure.dao.entity;

import com.inboyu.spring.cloud.starter.jpa.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 报修附件实体类
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "t_repair_attachment")
public class RepairAttachmentEntity extends BaseEntity {

    @Column(name = "attachment_id", nullable = false)
    private Long attachmentId;

    @Column(name = "repair_order_id", nullable = false)
    private Long repairOrderId;

    @Column(name = "file_name", nullable = false, length = 255)
    private String fileName;

    @Column(name = "file_url", nullable = false, length = 500)
    private String fileUrl;

    @Column(name = "file_type", nullable = false, length = 200)
    private String fileType;

    @Column(name = "attachment_type", nullable = false, length = 200)
    private String attachmentType;

    @Column(name = "file_size", nullable = false)
    private Long fileSize;

    @Column(name = "upload_time", nullable = false)
    private LocalDateTime uploadTime;
}
