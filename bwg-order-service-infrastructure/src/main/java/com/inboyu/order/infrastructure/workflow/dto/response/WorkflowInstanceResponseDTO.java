package com.inboyu.order.infrastructure.workflow.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 工作流实例响应DTO
 * 
 * <AUTHOR>
 */
@Data
public class WorkflowInstanceResponseDTO {
    
    @Schema(description = "工作流实例ID")
    private String workflowInstanceId;

    @Schema(description = "业务类型")
    private String businessType;

    @Schema(description = "业务ID")
    private String businessId;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "创建时间")
    private String createTime;

    @Schema(description = "实例详情JSON")
    private String instanceDetail;
}
