package com.inboyu.order.infrastructure.builder.impl;

import com.inboyu.order.domain.repair.model.RepairConfig;
import com.inboyu.order.domain.repair.model.RepairConfigId;
import com.inboyu.order.domain.repair.model.RepairTodayEnable;
import com.inboyu.order.domain.repair.model.StoreId;
import com.inboyu.order.infrastructure.builder.RepairConfigBuilder;
import com.inboyu.order.infrastructure.dao.entity.RepairConfigEntity;
import com.inboyu.constant.DeleteFlag;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 报修配置转换器实现
 * 负责领域对象与数据库实体之间的转换
 */
@Component
public class RepairConfigBuilderImpl implements RepairConfigBuilder {

    @Override
    public RepairConfigEntity toEntity(RepairConfig repairConfig) {
        if (repairConfig == null) {
            return null;
        }

        RepairConfigEntity entity = new RepairConfigEntity();
        entity.setConfigId(repairConfig.getId().getValue());
        entity.setStoreId(repairConfig.getStoreId().getValue());
        entity.setRepairStartTime(repairConfig.getRepairStartTime());
        entity.setRepairEndTime(repairConfig.getRepairEndTime());
        entity.setRepairTimeInterval(repairConfig.getRepairTimeInterval());
        entity.setRepairWeekDay(repairConfig.getRepairWeekDay());
        entity.setMaxRepairDays(repairConfig.getMaxRepairDays());
        entity.setRepairTodayEnable(repairConfig.getRepairTodayEnable().getCode());

        // 设置基础字段
        entity.setCreateTime(LocalDateTime.now());
        entity.setModifyTime(LocalDateTime.now());
        entity.setDeleted(DeleteFlag.NOT_DELETED);
        entity.setVersion(0L);

        return entity;
    }

    @Override
    public RepairConfig toDomain(RepairConfigEntity entity) {
        if (entity == null) {
            return null;
        }

        return RepairConfig.builder()
                .id(RepairConfigId.of(entity.getConfigId()))
                .storeId(StoreId.of(entity.getStoreId()))
                .repairStartTime(entity.getRepairStartTime())
                .repairEndTime(entity.getRepairEndTime())
                .repairTimeInterval(entity.getRepairTimeInterval())
                .repairWeekDay(entity.getRepairWeekDay())
                .maxRepairDays(entity.getMaxRepairDays())
                .repairTodayEnable(createRepairTodayEnable(entity.getRepairTodayEnable()))
                .build();
    }

    @Override
    public RepairConfigEntity toUpdateEntity(RepairConfig repairConfig) {
        if (repairConfig == null) {
            return null;
        }

        RepairConfigEntity entity = new RepairConfigEntity();
        entity.setConfigId(repairConfig.getId().getValue());
        entity.setStoreId(repairConfig.getStoreId().getValue());
        entity.setRepairStartTime(repairConfig.getRepairStartTime());
        entity.setRepairEndTime(repairConfig.getRepairEndTime());
        entity.setRepairTimeInterval(repairConfig.getRepairTimeInterval());
        entity.setRepairWeekDay(repairConfig.getRepairWeekDay());
        entity.setMaxRepairDays(repairConfig.getMaxRepairDays());
        entity.setRepairTodayEnable(repairConfig.getRepairTodayEnable().getCode());

        return entity;
    }

    /**
     * 根据编码创建RepairTodayEnable值对象
     *
     * @param code 编码
     * @return RepairTodayEnable值对象
     */
    private RepairTodayEnable createRepairTodayEnable(String code) {
        if ("status.enabled".equals(code)) {
            return RepairTodayEnable.ENABLED;
        } else if ("status.disabled".equals(code)) {
            return RepairTodayEnable.DISABLED;
        } else {
            return RepairTodayEnable.create(code, getRepairTodayEnableTitle(code));
        }
    }

    /**
     * 根据编码获取标题
     *
     * @param code 编码
     * @return 标题
     */
    private String getRepairTodayEnableTitle(String code) {
        return switch (code) {
            case "status.enabled" -> "启用";
            case "status.disabled" -> "停用";
            default -> code;
        };
    }
}
