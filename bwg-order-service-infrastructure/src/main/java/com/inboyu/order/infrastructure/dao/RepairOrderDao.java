package com.inboyu.order.infrastructure.dao;

import com.inboyu.order.infrastructure.dao.entity.RepairOrderEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 报修工单数据访问接口
 */
@Repository
public interface RepairOrderDao extends JpaRepository<RepairOrderEntity, Long> {

    /**
     * 根据报修工单ID和删除标志查询报修工单
     *
     * @param repairOrderId 报修工单ID
     * @param deleted       删除标志
     * @return 报修工单实体
     */
    RepairOrderEntity findByRepairOrderIdAndDeleted(Long repairOrderId, Integer deleted);

    /**
     * 更新报修工单
     *
     * @param entity 报修工单实体
     */
    @Modifying
    @Query("UPDATE RepairOrderEntity e SET " +
           "e.customerId = :#{#entity.customerId}, " +
           "e.storeId = :#{#entity.storeId}, " +
           "e.roomId = :#{#entity.roomId}, " +
           "e.buildingId = :#{#entity.buildingId}, " +
           "e.itemId = :#{#entity.itemId}, " +
           "e.workflowId = :#{#entity.workflowId}, " +
           "e.roomNumber = :#{#entity.roomNumber}, " +
           "e.area = :#{#entity.area}, " +
           "e.detail = :#{#entity.detail}, " +
           "e.contactPhone = :#{#entity.contactPhone}, " +
           "e.submitter = :#{#entity.submitter}, " +
           "e.willRepairDate = :#{#entity.willRepairDate}, " +
           "e.willRepairStart = :#{#entity.willRepairStart}, " +
           "e.willRepairEnd = :#{#entity.willRepairEnd}, " +
           "e.isEnter = :#{#entity.isEnter}, " +
           "e.status = :#{#entity.status}, " +
           "e.orderType = :#{#entity.orderType}, " +
           "e.tagStatus = :#{#entity.tagStatus}, " +
           "e.refuseReason = :#{#entity.refuseReason}, " +
           "e.suspendReason = :#{#entity.suspendReason}, " +
           "e.preDoorTime = :#{#entity.preDoorTime}, " +
           "e.preRepairTime = :#{#entity.preRepairTime}, " +
           "e.finishTime = :#{#entity.finishTime}, " +
           "e.dealTime = :#{#entity.dealTime}, " +
           "e.responsibleId = :#{#entity.responsibleId}, " +
           "e.maintainerId = :#{#entity.maintainerId}, " +
           "e.remark = :#{#entity.remark}, " +
           "e.modifyTime = CURRENT_TIMESTAMP, " +
           "e.version = e.version + 1 " +
           "WHERE e.repairOrderId = :#{#entity.repairOrderId} AND e.deleted = 0")
    void update(@Param("entity") RepairOrderEntity entity);
}
