package com.inboyu.order.infrastructure.dao;

import com.inboyu.order.infrastructure.dao.entity.RepairOrderEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 报修工单数据访问接口
 */
@Repository
public interface RepairOrderDao extends JpaRepository<RepairOrderEntity, Long> {

    /**
     * 根据报修工单ID和删除标志查询报修工单
     *
     * @param repairOrderId 报修工单ID
     * @param deleted       删除标志
     * @return 报修工单实体
     */
    Optional<RepairOrderEntity> findByRepairOrderIdAndDeleted(Long repairOrderId, Integer deleted);

    /**
     * 批量保存报修工单
     *
     * @param entities 报修工单实体列表
     * @return 保存后的报修工单实体列表
     */
    @Override
    <S extends RepairOrderEntity> List<S> saveAll(Iterable<S> entities);

    /**
     * 根据客户ID查询报修工单列表
     *
     * @param customerId 客户ID
     * @param deleted    删除标识
     * @return 报修工单列表
     */
    List<RepairOrderEntity> findByCustomerIdAndDeleted(Long customerId, Integer deleted);

    /**
     * 根据门店ID查询报修工单列表
     *
     * @param storeId 门店ID
     * @param deleted 删除标识
     * @return 报修工单列表
     */
    List<RepairOrderEntity> findByStoreIdAndDeleted(Long storeId, Integer deleted);

    /**
     * 更新报修工单
     *
     * @param entity 报修工单实体
     */
    @Modifying
    @Query("UPDATE RepairOrderEntity e SET " +
            "e.customerId = :#{#entity.customerId}, " +
            "e.storeId = :#{#entity.storeId}, " +
            "e.roomId = :#{#entity.roomId}, " +
            "e.buildingId = :#{#entity.buildingId}, " +
            "e.itemId = :#{#entity.itemId}, " +
            "e.workflowId = :#{#entity.workflowId}, " +
            "e.roomNumber = :#{#entity.roomNumber}, " +
            "e.area = :#{#entity.area}, " +
            "e.detail = :#{#entity.detail}, " +
            "e.contactPhone = :#{#entity.contactPhone}, " +
            "e.submitter = :#{#entity.submitter}, " +
            "e.willRepairDate = :#{#entity.willRepairDate}, " +
            "e.willRepairStart = :#{#entity.willRepairStart}, " +
            "e.willRepairEnd = :#{#entity.willRepairEnd}, " +
            "e.isEnter = :#{#entity.isEnter}, " +
            "e.status = :#{#entity.status}, " +
            "e.orderType = :#{#entity.orderType}, " +
            "e.tagStatus = :#{#entity.tagStatus}, " +
            "e.refuseReason = :#{#entity.refuseReason}, " +
            "e.suspendReason = :#{#entity.suspendReason}, " +
            "e.preDoorTime = :#{#entity.preDoorTime}, " +
            "e.preRepairTime = :#{#entity.preRepairTime}, " +
            "e.finishTime = :#{#entity.finishTime}, " +
            "e.dealTime = :#{#entity.dealTime}, " +
            "e.responsibleId = :#{#entity.responsibleId}, " +
            "e.maintainerId = :#{#entity.maintainerId}, " +
            "e.remark = :#{#entity.remark}, " +
            "e.modifyTime = CURRENT_TIMESTAMP, " +
            "e.version = e.version + 1 " +
            "WHERE e.repairOrderId = :#{#entity.repairOrderId} AND e.deleted = 0")
    void update(@Param("entity") RepairOrderEntity entity);

    /**
     * 小程序端分页查询报修订单列表
     *
     * @param customerId    客户ID
     * @param storeId       门店ID
     * @param statusList    状态列表
     * @param responsibleId 负责人ID
     * @param roomId        房间ID
     * @param roomNum       房号（模糊搜索）
     * @param pageable      分页参数
     * @return 分页结果
     */
    @Query("SELECT ro FROM RepairOrderEntity ro WHERE ro.deleted = 0 " +
            "AND (:customerId IS NULL OR ro.customerId = :customerId) " +
            "AND (:storeId IS NULL OR ro.storeId = :storeId) " +
            "AND (:statusList IS NULL OR ro.status IN :statusList) " +
            "AND (:responsibleId IS NULL OR ro.responsibleId = :responsibleId) " +
            "AND (:roomId IS NULL OR ro.roomId = :roomId) " +
            "AND (:roomNum IS NULL OR ro.roomNumber LIKE %:roomNum%) " +
            "ORDER BY ro.createTime DESC")
    Page<RepairOrderEntity> pageByFilters(
            @Param("customerId") Long customerId,
            @Param("storeId") Long storeId,
            @Param("statusList") List<String> statusList,
            @Param("responsibleId") Long responsibleId,
            @Param("roomId") Long roomId,
            @Param("roomNum") String roomNum,
            Pageable pageable);
}
