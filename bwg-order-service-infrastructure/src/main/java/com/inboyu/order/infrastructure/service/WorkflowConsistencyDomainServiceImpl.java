package com.inboyu.order.infrastructure.service;

import com.inboyu.order.domain.repair.model.RepairOrder;
import com.inboyu.order.domain.repair.model.RepairOrderId;
import com.inboyu.order.domain.repair.model.RepairOrderStatus;
import com.inboyu.order.domain.repair.service.WorkflowConsistencyDomainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 工作流状态一致性领域服务实现
 * 负责确保报修订单状态与工作流状态的一致性
 * 位于infrastructure层，可以处理日志记录等基础设施功能
 */
@Slf4j
@Service
public class WorkflowConsistencyDomainServiceImpl implements WorkflowConsistencyDomainService {

    // 工作流状态常量
    private static final String WORKFLOW_STATUS_SUBMITTED = "instance_status.submitted";
    private static final String WORKFLOW_STATUS_APPROVE_PASS = "instance_status.approve_pass";
    private static final String WORKFLOW_STATUS_APPROVE_REJECT = "instance_status.approve_reject";
    private static final String WORKFLOW_STATUS_COMPLETED = "instance_status.completed";
    private static final String WORKFLOW_STATUS_CONFIRMED = "instance_status.confirmed";
    private static final String WORKFLOW_STATUS_REWORK = "instance_status.rework";
    private static final String WORKFLOW_STATUS_CANCELLED = "instance_status.cancelled";

    // 节点类型常量
    private static final String NODE_TYPE_SUBMIT = "node_type.submit";
    private static final String NODE_TYPE_APPROVAL = "node_type.approval";
    private static final String NODE_TYPE_COMPLETE = "node_type.complete";
    private static final String NODE_TYPE_CONFIRM = "node_type.confirm";

    // 状态转换规则映射
    private static final Map<String, String[]> STATUS_TRANSITION_RULES = new HashMap<>();

    static {
        // 已提单 -> 处理中/已关闭
        STATUS_TRANSITION_RULES.put(RepairOrderStatus.REPAIR_ORDER_STATUS_SUBMITTED, 
            new String[]{RepairOrderStatus.REPAIR_ORDER_STATUS_PROCESSING, RepairOrderStatus.REPAIR_ORDER_STATUS_CLOSED});
        
        // 处理中 -> 已完成/已关闭
        STATUS_TRANSITION_RULES.put(RepairOrderStatus.REPAIR_ORDER_STATUS_PROCESSING, 
            new String[]{RepairOrderStatus.REPAIR_ORDER_STATUS_COMPLETED, RepairOrderStatus.REPAIR_ORDER_STATUS_CLOSED});
        
        // 已完成 -> 已关闭
        STATUS_TRANSITION_RULES.put(RepairOrderStatus.REPAIR_ORDER_STATUS_COMPLETED, 
            new String[]{RepairOrderStatus.REPAIR_ORDER_STATUS_CLOSED});
        
        // 已关闭 -> 无转换（终态）
        STATUS_TRANSITION_RULES.put(RepairOrderStatus.REPAIR_ORDER_STATUS_CLOSED, 
            new String[]{});
    }

    @Override
    public boolean validateStatusConsistency(RepairOrder repairOrder, String workflowStatus, String nodeType) {
        if (repairOrder == null) {
            log.warn("报修订单为空，无法验证状态一致性");
            return false;
        }

        String orderStatus = repairOrder.getStatus().getCode();
        
        // 根据工作流状态和节点类型验证一致性
        boolean isConsistent = switch (nodeType) {
            case NODE_TYPE_SUBMIT -> WORKFLOW_STATUS_SUBMITTED.equals(workflowStatus) && 
                RepairOrderStatus.REPAIR_ORDER_STATUS_SUBMITTED.equals(orderStatus);
            
            case NODE_TYPE_APPROVAL -> {
                if (WORKFLOW_STATUS_APPROVE_PASS.equals(workflowStatus)) {
                    yield RepairOrderStatus.REPAIR_ORDER_STATUS_PROCESSING.equals(orderStatus);
                } else if (WORKFLOW_STATUS_APPROVE_REJECT.equals(workflowStatus)) {
                    yield RepairOrderStatus.REPAIR_ORDER_STATUS_CLOSED.equals(orderStatus);
                }
                yield false;
            }
            
            case NODE_TYPE_COMPLETE -> WORKFLOW_STATUS_COMPLETED.equals(workflowStatus) && 
                RepairOrderStatus.REPAIR_ORDER_STATUS_COMPLETED.equals(orderStatus);
            
            case NODE_TYPE_CONFIRM -> {
                if (WORKFLOW_STATUS_CONFIRMED.equals(workflowStatus)) {
                    yield RepairOrderStatus.REPAIR_ORDER_STATUS_CLOSED.equals(orderStatus);
                } else if (WORKFLOW_STATUS_REWORK.equals(workflowStatus)) {
                    yield RepairOrderStatus.REPAIR_ORDER_STATUS_PROCESSING.equals(orderStatus);
                }
                yield false;
            }
            
            default -> {
                log.warn("未知的节点类型: {}", nodeType);
                yield false;
            }
        };

        if (!isConsistent) {
            log.warn("状态不一致 - 订单状态: {}, 工作流状态: {}, 节点类型: {}", 
                orderStatus, workflowStatus, nodeType);
            recordStatusInconsistency(repairOrder.getRepairOrderId(), orderStatus, workflowStatus, nodeType);
        }

        return isConsistent;
    }

    @Override
    public RepairOrder updateOrderStatusByWorkflow(RepairOrder repairOrder, String workflowStatus, String nodeType) {
        if (repairOrder == null) {
            throw new IllegalArgumentException("报修订单不能为空");
        }

        String currentStatus = repairOrder.getStatus().getCode();
        String targetStatus = getTargetStatusByWorkflow(workflowStatus, nodeType);

        if (targetStatus == null) {
            log.warn("无法根据工作流状态确定目标状态 - 工作流状态: {}, 节点类型: {}", workflowStatus, nodeType);
            return repairOrder;
        }

        // 验证状态转换是否合法
        if (!isValidStatusTransition(currentStatus, targetStatus)) {
            log.error("非法的状态转换 - 从 {} 到 {}", currentStatus, targetStatus);
            throw new IllegalStateException("非法的状态转换: " + currentStatus + " -> " + targetStatus);
        }

        log.info("更新报修订单状态 - 订单ID: {}, 从 {} 到 {}", 
            repairOrder.getRepairOrderId().getValue(), currentStatus, targetStatus);

        return repairOrder.toBuilder()
            .status(RepairOrderStatus.of(targetStatus))
            .build();
    }

    @Override
    public boolean isValidStatusTransition(String currentStatus, String targetStatus) {
        if (currentStatus == null || targetStatus == null) {
            return false;
        }

        if (currentStatus.equals(targetStatus)) {
            return true; // 相同状态认为是合法的
        }

        String[] allowedStatuses = STATUS_TRANSITION_RULES.get(currentStatus);
        if (allowedStatuses == null) {
            log.warn("未知的当前状态: {}", currentStatus);
            return false;
        }

        return Arrays.asList(allowedStatuses).contains(targetStatus);
    }

    @Override
    public String[] getAllowedNextStatuses(String currentStatus) {
        return STATUS_TRANSITION_RULES.getOrDefault(currentStatus, new String[0]);
    }

    @Override
    public void recordStatusInconsistency(RepairOrderId repairOrderId, String orderStatus, String workflowStatus, String nodeType) {
        log.error("记录状态不一致异常 - 订单ID: {}, 订单状态: {}, 工作流状态: {}, 节点类型: {}", 
            repairOrderId.getValue(), orderStatus, workflowStatus, nodeType);
        
        // 这里可以添加更多的记录逻辑，比如：
        // 1. 发送告警通知
        // 2. 记录到专门的异常表
        // 3. 触发人工干预流程
        // 4. 记录到监控系统
    }

    /**
     * 根据工作流状态和节点类型确定目标订单状态
     */
    private String getTargetStatusByWorkflow(String workflowStatus, String nodeType) {
        return switch (nodeType) {
            case NODE_TYPE_SUBMIT -> RepairOrderStatus.REPAIR_ORDER_STATUS_SUBMITTED;
            
            case NODE_TYPE_APPROVAL -> {
                if (WORKFLOW_STATUS_APPROVE_PASS.equals(workflowStatus)) {
                    yield RepairOrderStatus.REPAIR_ORDER_STATUS_PROCESSING;
                } else if (WORKFLOW_STATUS_APPROVE_REJECT.equals(workflowStatus)) {
                    yield RepairOrderStatus.REPAIR_ORDER_STATUS_CLOSED;
                }
                yield null;
            }
            
            case NODE_TYPE_COMPLETE -> RepairOrderStatus.REPAIR_ORDER_STATUS_COMPLETED;
            
            case NODE_TYPE_CONFIRM -> {
                if (WORKFLOW_STATUS_CONFIRMED.equals(workflowStatus)) {
                    yield RepairOrderStatus.REPAIR_ORDER_STATUS_CLOSED;
                } else if (WORKFLOW_STATUS_REWORK.equals(workflowStatus)) {
                    yield RepairOrderStatus.REPAIR_ORDER_STATUS_PROCESSING;
                }
                yield null;
            }
            
            default -> null;
        };
    }
}
