package com.inboyu.order.infrastructure.builder;

import com.inboyu.order.domain.repair.model.RepairAttachment;
import com.inboyu.order.infrastructure.entity.RepairAttachmentEntity;

import java.util.List;

/**
 * 报修附件Builder接口
 * 用于在领域模型和实体类之间进行转换
 */
public interface RepairAttachmentBuilder {

    /**
     * 将实体类转换为领域模型
     */
    RepairAttachment toDomain(RepairAttachmentEntity entity);

    /**
     * 将领域模型转换为实体类
     */
    RepairAttachmentEntity toEntity(RepairAttachment domain);

    /**
     * 批量将实体类列表转换为领域模型列表
     */
    List<RepairAttachment> toDomains(List<RepairAttachmentEntity> entities);

    /**
     * 批量将领域模型列表转换为实体类列表
     */
    List<RepairAttachmentEntity> toEntities(List<RepairAttachment> domains);
}
