# 门店报修后台配置功能实现

## 功能概述

本次实现了门店报修后台配置功能，包括以下核心功能：

1. **获取系统级所有报修物品列表**（无需分页）
2. **新增门店报修配置**（同时配置门店可报修物品）
3. **修改门店报修配置**（同时修改门店可报修物品配置）
4. **查询门店报修配置详情**（包含门店可报修物品）

## 技术架构

采用DDD（领域驱动设计）分层架构，严格按照项目现有的代码风格和规范实现：

### 1. 领域层 (Domain Layer)
- **值对象 (Value Objects)**
  - `RepairConfigId`: 报修配置ID值对象
  - `RepairItemId`: 报修物品ID值对象  
  - `StoreId`: 门店ID值对象
  - `RepairWeekDay`: 报修服务日值对象
  - `RepairTodayEnable`: 当天报修开放状态值对象

- **领域对象 (Domain Objects)**
  - `RepairConfig`: 报修配置领域对象，包含业务行为
  - `RepairItem`: 报修物品领域对象，包含业务行为

- **仓储接口 (Repository Interfaces)**
  - `RepairConfigRepository`: 报修配置仓储接口
  - `RepairItemRepository`: 报修物品仓储接口

- **领域服务 (Domain Services)**
  - `RepairConfigDomainService`: 报修配置领域服务接口
  - `RepairConfigDomainServiceImpl`: 报修配置领域服务实现

### 2. 基础设施层 (Infrastructure Layer)
- **实体类 (Entities)**
  - `RepairConfigEntity`: 报修配置数据库实体
  - `RepairItemEntity`: 报修物品数据库实体
  - `RepairStoreItemEntity`: 门店报修物品配置数据库实体

- **DAO接口 (Data Access Objects)**
  - `RepairConfigDao`: 报修配置数据访问接口
  - `RepairItemDao`: 报修物品数据访问接口
  - `RepairStoreItemDao`: 门店报修物品配置数据访问接口

- **转换器 (Builders)**
  - `RepairConfigBuilder`: 报修配置转换器接口
  - `RepairConfigBuilderImpl`: 报修配置转换器实现
  - `RepairItemBuilder`: 报修物品转换器接口
  - `RepairItemBuilderImpl`: 报修物品转换器实现

- **仓储实现 (Repository Implementations)**
  - `RepairConfigRepositoryImpl`: 报修配置仓储实现
  - `RepairItemRepositoryImpl`: 报修物品仓储实现

### 3. 应用层 (Application Layer)
- **应用服务 (Application Services)**
  - `RepairConfigAppService`: 报修配置应用服务接口
  - `RepairConfigAppServiceImpl`: 报修配置应用服务实现

- **DTO类 (Data Transfer Objects)**
  - `RepairConfigCreateRequestDTO`: 新增报修配置请求DTO
  - `RepairConfigUpdateRequestDTO`: 修改报修配置请求DTO
  - `RepairTodayEnableDTO`: 当天报修开放状态DTO
  - `RepairItemResponseDTO`: 报修物品响应DTO
  - `RepairConfigDetailResponseDTO`: 报修配置详情响应DTO

- **转换器 (Converters)**
  - `RepairConfigAppConverter`: 应用层转换器

### 4. 应用层控制器 (Application Layer Controller)
- **控制器 (Controllers)**
  - `RepairConfigController`: 门店报修配置控制器（位于app层）

## API接口

### 1. 获取所有报修物品列表
```
GET /api/v1/repair-configs/repair-items
```

### 2. 新增门店报修配置
```
POST /api/v1/repair-configs
```

### 3. 修改门店报修配置
```
PUT /api/v1/repair-configs
```

### 4. 查询门店报修配置详情
```
GET /api/v1/repair-configs/{configId}
```

## 数据库表结构

### 1. t_repair_config (报修配置表)
- `config_id`: 配置业务ID
- `store_id`: 门店ID
- `repair_start_time`: 报修开始时间
- `repair_end_time`: 报修结束时间
- `repair_time_interval`: 报修时段时长
- `repair_week_day`: 每周服务日
- `max_repair_days`: 最远可预约日期
- `repair_today_enable`: 是否开放当天预约

### 2. t_repair_item (报修物品表)
- `item_id`: 物品业务ID
- `kind`: 物品分类
- `name`: 物品名称
- `icon`: 物品图标
- `url`: Logo地址
- `remark`: 故障描述标签
- `sort_order`: 排序
- `is_active`: 是否启用

### 3. t_repair_store_item (门店物品配置表)
- `config_id`: 配置业务ID
- `store_id`: 门店ID
- `item_id`: 物品ID

## 设计特点

1. **严格遵循DDD分层架构**：每层职责明确，依赖方向正确
2. **领域对象丰富**：包含业务行为，避免贫血模型
3. **值对象封装**：ID类型和状态类型都封装为值对象，确保类型安全
4. **接口参数规范**：接收String类型的业务ID，内部转换为Long类型
5. **响应参数规范**：返回String类型的业务ID
6. **使用ORM**：JPA Criteria API进行类型安全查询
7. **依赖注入**：使用@Autowired，避免手动new对象
8. **参数验证**：使用注解进行输入验证
9. **API文档**：使用Swagger注解生成文档
10. **异常处理**：统一的错误码管理和异常处理

## 错误码定义

在`ResponseCode`枚举中定义了报修相关的错误码（15000-15299）：

- `15000`: 报修配置不存在
- `15001`: 该门店已存在报修配置，请勿重复创建
- `15002`: 报修时间配置不合理，开始时间必须早于结束时间
- `15003`: 门店不存在
- `15100`: 报修物品不存在
- `15101`: 报修物品未启用
- `15200`: ID格式不正确
- `15201`: 操作失败

## 测试

提供了简单的集成测试用例，测试RepairConfigAppServiceImpl的4个方法：
- `getAllRepairItems()`: 获取所有报修物品列表
- `createRepairConfig()`: 创建门店报修配置
- `updateRepairConfig()`: 修改门店报修配置
- `getRepairConfigDetail()`: 查询门店报修配置详情

同时包含异常场景测试：
- 创建配置失败（门店已存在配置）
- 查询配置详情失败（配置不存在）

测试采用SpringBootTest集成测试方式，直接注入app层Controller进行测试，无需Mock依赖。

## 使用说明

1. **获取报修物品列表**：调用GET接口获取系统中所有启用的报修物品
2. **新增配置**：提供门店ID、时间配置、服务日配置和可报修物品ID列表
3. **修改配置**：提供配置ID和更新的配置信息
4. **查询详情**：根据配置ID查询完整的配置信息，包含可报修物品详情

所有接口都遵循RESTful设计规范，使用统一的响应格式，并提供完整的Swagger文档。

## 运行测试

运行集成测试来验证功能：

```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=RepairConfigControllerTest

# 运行特定测试方法
mvn test -Dtest=RepairConfigControllerTest#getAllRepairItemsTest
```

**注意**：运行测试前请确保：
1. 数据库连接正常
2. 相关数据表已创建
3. 系统上下文配置正确（TenantId等）

## 部署说明

1. **数据库准备**：执行DDL脚本创建相关数据表
2. **数据初始化**：手动插入报修物品数据到`t_repair_item`表
3. **服务启动**：启动order-service-app服务
4. **接口测试**：通过Swagger UI或Postman测试接口功能
