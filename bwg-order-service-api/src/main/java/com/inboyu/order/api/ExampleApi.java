package com.inboyu.order.api;

import com.inboyu.order.constant.ApiInfo;
import com.inboyu.order.dto.request.HelloWorldRequest;
import com.inboyu.order.dto.response.HelloWorldResponse;
import com.inboyu.spring.cloud.starter.api.ApiClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * <AUTHOR>
 * @date 2025年07月22日 16:25
 */
@Component
@ApiClient(url = ApiInfo.API_URL)
public interface ExampleApi {
    @PostMapping("/example/helloWorld")
    HelloWorldResponse helloWorld(HelloWorldRequest request);
}
