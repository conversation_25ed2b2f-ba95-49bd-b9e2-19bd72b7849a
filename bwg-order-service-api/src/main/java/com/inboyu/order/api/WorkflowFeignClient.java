package com.inboyu.order.api;

import com.inboyu.admin.dto.request.workflow.InstanceApprovalRequestDTO;
import com.inboyu.admin.dto.request.workflow.InstanceCancelRequestDTO;
import com.inboyu.admin.dto.request.workflow.InstanceCreateRequestDTO;
import com.inboyu.admin.dto.response.workflow.InstanceCancelResponseDTO;
import com.inboyu.admin.dto.response.workflow.InstanceCreateResponseDTO;
import com.inboyu.admin.dto.response.workflow.InstanceDTO;
import com.inboyu.admin.dto.response.workflow.InstanceStepBasicDTO;
import com.inboyu.order.constant.ApiInfo;
import com.inboyu.spring.cloud.starter.api.ApiClient;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 工作流 Feign 客户端
 * 用于调用 bwg-admin-service 中的工作流相关接口
 *
 */
@ApiClient(url = ApiInfo.ADMIN_SERVICE_URL)
public interface WorkflowFeignClient {

    /**
     * 预览工作流实例
     *
     * @param request 预览请求参数
     * @return 预览结果
     */
 /*   @PostMapping("/preview")
    @Operation(summary = "预览工作流实例")
    InstancePreviewResponseDTO preview(@RequestBody @Valid InstancePreviewRequestDTO request);*/

    /**
     * 创建工作流实例
     *
     * @param request 创建请求参数
     * @return 创建结果
     */
    @PostMapping("/api/v1/workflow/create")
    @Operation(summary = "创建工作流实例")
    InstanceCreateResponseDTO create(@RequestBody @Valid InstanceCreateRequestDTO request);

    /**
     * 工作流审批
     * 
     * @param instanceStepId 实例步骤ID
     * @param request 审批请求参数
     * @return 审批结果
     */
    @PostMapping("/api/v1/workflow/{instanceStepId}/approval")
    @Operation(summary = "工作流审批")
    InstanceStepBasicDTO approval(@PathVariable String instanceStepId,
                                 @RequestBody @Valid InstanceApprovalRequestDTO request);
    /**
     * 工作流撤销
     *
     * @param instanceId 实例ID
     * @param request 撤销请求参数
     * @return 撤销结果
     */
    @PostMapping("/api/v1/workflow/{instanceId}/cancel")
    @Operation(summary = "工作流撤销")
    InstanceCancelResponseDTO cancel(@PathVariable String instanceId,
                                    @RequestBody @Valid InstanceCancelRequestDTO request);

    /**
     * 查询业务单最新的工作流实例
     *
     * @param workflowInstanceId 工作流实例ID
     * @return 工作流实例信息
     */
    @GetMapping("/api/v1/workflow/business/{workflowInstanceId}")
    @Operation(summary = "查询业务单最新的工作流实例")
    InstanceDTO getWorkflowInstance(@Schema(description = "工作流实例ID")
                                   @PathVariable String workflowInstanceId);

    /**
     * 新增步骤（支持挂起、审批转交）
     *
     * @param workflowInstanceId 工作流实例ID
     * @param form 新增步骤请求参数
     * @return 新增步骤结果
     */
    /*@PostMapping("/{workflowInstanceId}/step")
    @Operation(summary = "新增步骤（标准接口）支持挂起、审批转交")
    InstanceNewStepDTO newStep(@Schema(description = "工作流实例ID")
                              @PathVariable String workflowInstanceId,
                              @RequestBody @Valid InstanceNewStepRequestDTO form);*/
}